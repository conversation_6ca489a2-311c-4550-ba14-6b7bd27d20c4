// printer.js
import bleTool from '../../SUPVANAPIT50PRO/BLETool.js'
import bleToothManage from '../../SUPVANAPIT50PRO/BLEToothManage.js'
import constants from '../../SUPVANAPIT50PRO/Constants.js'

// 导入配置文件
const onlineStoreConfig = require('../../config/onlineStore.js');
const {
  getStatusMessage,
  getErrorLevel,
  getStatusCategory,
  isSuccessStatus
} = require('../../config/printerStatus.js');

const { checkPrinterDevice, addPrintRecord } = require('../../config/api.js');


// 模板服务（重构到 config/templates.js）
const templateService = require('../../config/templates.js')
// 异常帮助提示配置
const { getHelpTipByContext, hasHelpTipForContext } = require('../../config/helpTips.js')

Page({

  // 用户OpenID，可在整个页面中使用
  userOpenId: '',

  // OpenID检查轮询定时器
  openIdCheckTimer: null,
  // BLE连接状态监听是否已注册
  connectionListenerRegistered: false,

  data: {
    // 打印机状态
    printerStatus: 'disconnected', // disconnected, connecting, disconnecting, error, connected, printing
    printerDeviceSn: '',
    printerErrorCode: null, // 错误状态码
    printerErrorMessage: '', // 错误信息
    isPrinting: false, // 是否正在打印
    isConnecting: false, // 是否正在连接
    printTimeoutTimer: null, // 打印超时定时器
    connectionCheckTimer: null, // 连接在线检查定时器
    showCustomAdModal: false, // 是否显示定制广告弹窗
    adImageScrollable: false, // 广告图片是否可滚动
    adImageScrollIndicatorOpacity: 0, // 广告图片滚动指示器透明度
    adImageUrl: 'https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/ad.jpg', // 广告图片 URL
    // 模版相关
    templates: [], // 模版列表
    selectedTemplateId: null, // 当前选中的模版ID
    currentTemplateName: '', // 当前选中的模版名称
    globalSelectedTemplateId: null, // 全局选中的模版ID（用于跨分类显示和跳转）
    globalSelectedTemplateName: '', // 全局选中的模版名称
    globalSelectedCategoryName: '', // 全局选中模版所在的分类名称
    scrollIntoViewId: '', // 滚动到指定元素的ID
    showTemplateHelp: false, // 是否显示模板帮助提示

    // 模版分类与分页
    categoryList: [], // [{categoryId, categoryName}]
    selectedCategoryName: '', // 当前选中的分类名称（无分类时为空）
    templatePageIndex: 0,
    templatePageSize: 20,

    templateScrollState: { // 模版列表滚动状态
      canScrollLeft: false,
      canScrollRight: false,
      showHint: false,
      hintText: '',
      hintTimer: null,
      containerWidth: 0
    },

    // 远程模板分页状态
    templateTotal: 0,
    isLoadingTemplates: false,

    // 预加载阈值（接近右边界 N 个卡片时预取）
    nearEndPrefetchCount: 5,
    templateItemRpxTotal: 260, // 估算：宽200 + padding40 + margin-right20 = 260rpx
    prefetchThresholdPx: 0,
    previewImagePath: '', // 预览图路径
    previewGenerating: false, // 是否正在生成预览图
    currentPreviewId: 0, // 当前预览图生成ID，用于防止竞态条件

    // 动态输入（根据模板的 DrawObjects 中 IsInput 构建）
    dynamicInputs: [], // [{ drawIndex, title, format, regex, max, options, value, datePart, timePart, secondPart, charCount }]

    // 时间选择器数据
    hourRange: Array.from({length: 24}, (_, i) => String(i).padStart(2, '0')),
    minuteRange: Array.from({length: 60}, (_, i) => String(i).padStart(2, '0')),
    secondRange: Array.from({length: 60}, (_, i) => String(i).padStart(2, '0')),

    // 标签内容（兼容旧逻辑，后续用 dynamicInputs 替代）
    labelContent: {
      productName: '',
      operator: '',
      date: '',
      copies: 1
    },

    // 输入字符计数（旧逻辑，仅用于兼容现有UI/存储）
    inputCharCount: {
      productName: 0,
      operator: 0
    },

    // 蓝牙相关
    blueList: [],
    isScanning: false,
    showDeviceSelector: false,
    connectingDeviceId: '', // 正在连接中的设备ID

    // 联系我们弹窗
    showContactModal: false,

    // 首次进入连接提示
    showConnectPrompt: false,



    // 状态帮助提示（“？”）
    showStatusHelpModal: false, // 是否显示状态帮助弹窗
    helpImageScrollable: false, // 帮助图片是否可滚动
    helpImageScrollIndicatorOpacity: 0, // 帮助图片滚动指示器透明度
    statusHelpImageUrl: '',
    statusHelpTipAvailable: false,

    // 系统信息
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 40, // 导航栏高度，默认40px

    // Canvas相关
    templateWidth: 560,
    templateHeight: 302,
    barCodeWidth: 214,
    barCodeHeight: 72,
    qrCodeWidth: 20,
    qrCodeHeight: 20,
    pixelRatio: 2,
    canvasBarCode: null,
    canvasText: null,

    // 耗材信息相关
    materialInfo: null, // 当前耗材信息
    materialMismatch: false, // 耗材规格是否不匹配
    forcePrint: true, // 是否允许强制打印
    checkPrinterBinding: true // 是否检查打印机绑定
  },



  onLoad() {
    this.getSystemInfo()
    this.initPage()
  },

  /**
   * 根据 TemplateId 查找模板
   */
  findTemplateById(templateId) {
    if (!this.data.templates || !Array.isArray(this.data.templates)) return null
    return this.data.templates.find(template => template.TemplateId === templateId) || null
  },

  /**
   * 根据 TemplateId 查找模板在列表中的索引
   */
  findTemplateIndexById(templateId) {
    if (!this.data.templates || !Array.isArray(this.data.templates)) return -1
    return this.data.templates.findIndex(template => template.TemplateId === templateId)
  },

  /**
   * 获取当前选中的模板对象
   */
  getCurrentTemplate() {
    return this.findTemplateById(this.data.selectedTemplateId)
  },

  onShow() {
    // 进入页面后，如果未连接，显示连接提示弹窗
    if (this.data.printerStatus !== 'connected') {
      this.setData({ showConnectPrompt: true })
    }

    this.initTemplateCategoriesAndLoad()

    // // 页面显示时，延迟滚动到当前选中的标签模版项
    setTimeout(() => {
      const templateIndex = this.findTemplateIndexById(this.data.selectedTemplateId)
      if (templateIndex >= 0) {
        this.setData({
          scrollIntoViewId: `template-${templateIndex}`
        })
      }
    }, 300)

  },




  onReady() {
    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()
    this.checkOpenIdAvailability()
  },

  onUnload(){
    // 页面卸载时清除定时器
    if (this.openIdCheckTimer) {
      clearInterval(this.openIdCheckTimer);
      this.openIdCheckTimer = null;
    }
    // 清除打印超时定时器
    if (this.data.printTimeoutTimer) {
      clearTimeout(this.data.printTimeoutTimer)
    }

    // 停止BLE连接状态监听与在线检测
    this.stopConnectionMonitoring()

    this.performDisconnect()
  },
  /**
   * 检查OpenID是否可用
   */
  checkOpenIdAvailability() {
    if (this.userOpenId) {
      // OpenID已存在，直接执行后续逻辑
      this.onOpenIdReady();
    } else {
      // OpenID未获取到，启动轮询检查
      this.openIdCheckTimer = setInterval(() => {
        const openId = wx.getStorageSync('openId');
        if (openId) {
          this.userOpenId = openId;
          clearInterval(this.openIdCheckTimer);
          this.openIdCheckTimer = null;
          this.onOpenIdReady();
        }
      }, 500);
    }
  },

  /**
   * OpenID可用后的回调
   */
  onOpenIdReady() {
    // 在这里执行依赖OpenID的逻辑
    console.log('OpenID已就绪:', this.userOpenId);
    this.setData({
      userOpenId: this.userOpenId
    });
  },

  /**
   * 获取系统信息，设置状态栏和导航栏高度
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()
    const { statusBarHeight, platform, windowWidth } = systemInfo

    // 设置状态栏高度
    this.setData({
      statusBarHeight: statusBarHeight || 20,
      'templateScrollState.containerWidth': windowWidth
    })

    // 根据平台设置导航栏高度
    let navBarHeight = 40 // iOS默认高度，减小一些
    if (platform === 'android') {
      navBarHeight = 46 // Android稍高一些
    }

    this.setData({
      navBarHeight: navBarHeight
    })

    console.log('系统信息:', { statusBarHeight, navBarHeight, platform })
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 加载上次保存的标签内容
    const savedContent = wx.getStorageSync('lastLabelContent')
    const labelContent = savedContent ? {
      ...savedContent,
      date: savedContent.date || '' // 保持上次保存的日期，如果没有则为空
    } : {
      productName: '',
      operator: '',
      date: '', // 默认为空，用户可以选择是否设置日期
      copies: 1
    }

    this.setData({
      labelContent: labelContent,
      inputCharCount: {
        productName: labelContent.productName.length,
        operator: labelContent.operator.length
      },
      pixelRatio: wx.getWindowInfo().pixelRatio,
      canvasText: wx.createCanvasContext('Canvas', this),
      canvasBarCode: wx.createSelectorQuery().in(this),
    })

    console.log('设备检查功能状态:', this.data.checkPrinterBinding ? '启用' : '禁用')

    // 加载分类并按需加载模版（无分类则以空分类加载）
    // this.initTemplateCategoriesAndLoad()

    // 初始化分类并加载模板方法（定义在 methods 区域后面）




    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()

	    // 初始化后计算一次状态帮助提示可见性
	    this.computeStatusHelpTip()

  },

  /**
   * 加载模版（支持分类与分页）
   * @param {string} categoryName 允许为空字符串表示无分类（全量）
   * @param {number} pageIndex
   * @param {boolean} append 是否追加到现有列表
   * @param {boolean} isInitialLoad 是否为初始化加载（初始化时才会自动选中第一个模板）
   */
  async loadTemplates(categoryName = '', pageIndex = 0, append = false, isInitialLoad = false) {
    // 只在初始化时使用全局记忆，不使用分类记忆
    const lastUsedState = isInitialLoad ? this.getLastUsedTemplateState() : null
    const lastSelectedTemplateId = lastUsedState ? lastUsedState.templateId : null
    const pageSize = this.data.templatePageSize || 20

    // 如果选择的分类是“定制标签”，需要把当前设备SN注入到 sceneTags
    let sceneTags
    if (categoryName === '定制标签') {
      sceneTags = this.data.printerDeviceSn || '无定制标识'
    }

    try {
      // throw new Error('跳过远端模版加载')
      const { templates, total } = await templateService.loadTemplates({ categoryName, sceneTags }, pageIndex, pageSize)

      if (templates && templates.length > 0) {
        const finalList = append ? (this.data.templates || []).concat(templates) : templates

        // 确定选中的模板ID
        let selectedTemplateId
        if (append) {
          selectedTemplateId = this.data.selectedTemplateId
        } else {
          // 切换分类时，检查全局选中的模板是否在新分类中存在
          const globalTemplateId = this.data.globalSelectedTemplateId
          if (globalTemplateId) {
            // 在新分类中查找全局选中的模板
            const foundTemplate = finalList.find(template => template.TemplateId === globalTemplateId)
            if (foundTemplate) {
              // 如果找到全局选中的模板，自动选中它
              selectedTemplateId = foundTemplate.TemplateId
            } else {
              // 如果没找到全局选中的模板，不自动选中任何模板
              selectedTemplateId = null
            }
          } else {
            // 没有全局选中模板时的处理
            if (isInitialLoad) {
              // 初始化加载：使用全局记录的最后选择，或选中第一个模板
              selectedTemplateId = lastSelectedTemplateId && finalList.find(t => t.TemplateId === lastSelectedTemplateId)
                ? lastSelectedTemplateId
                : (finalList.length > 0 ? finalList[0].TemplateId : null)
            } else {
              // 分类切换且无全局选中：不自动选中任何模板
              selectedTemplateId = null
            }
          }
        }

        const currentTemplate = selectedTemplateId ? finalList.find(t => t.TemplateId === selectedTemplateId) : null

        // 更新数据状态
        const updateData = {
          templates: finalList,
          selectedTemplateId: selectedTemplateId,
          currentTemplateName: currentTemplate ? currentTemplate.TemplateName : '',
          templatePageIndex: pageIndex,
          selectedCategoryName: categoryName,
          templateTotal: typeof total === 'number' ? total : (append ? (this.data.templateTotal || 0) + templates.length : templates.length),
          // 如果选中了模板，更新全局选中状态
          globalSelectedTemplateId: selectedTemplateId && currentTemplate ? selectedTemplateId : this.data.globalSelectedTemplateId,
          globalSelectedTemplateName: selectedTemplateId && currentTemplate ? currentTemplate.TemplateName : this.data.globalSelectedTemplateName,
          globalSelectedCategoryName: selectedTemplateId && currentTemplate ? categoryName : this.data.globalSelectedCategoryName
        }

        this.setData(updateData)

        // 保存选择到本地存储（按分类记忆）
        // 只有在初始化加载时才保存自动选择的结果
        if (!append && selectedTemplateId && isInitialLoad) {
          this.saveLastSelectedTemplateId(categoryName, selectedTemplateId)
        }

        // 只有在选中了模板时才构建动态输入和生成预览
        if (selectedTemplateId) {
          this.buildDynamicInputsFromTemplate()
          this.generatePreview()
        }
      } else {
        // 成功但无数据：不回退默认模板
        if (!append) {
          this.setData({
            templates: [],
            selectedTemplateId: null,
            currentTemplateName: '',
            templatePageIndex: pageIndex,
            selectedCategoryName: categoryName,
            templateTotal: typeof total === 'number' ? total : 0
          })
          // 无数据时不调用构建动态输入和生成预览
          this.initTemplateScrollState(false)
        }
      }

      // 切换分类时需要重置滚动位置
      this.initTemplateScrollState(!append)
    } catch (e) {
      console.warn('加载系统模板异常，回退默认模板:', e)
      if (!append) {
        const fallback = templateService.getFallbackTemplates()
        // 尝试使用记录的模板ID，如果找不到且是初始化加载则使用第一个
        const fallbackSelectedId = lastSelectedTemplateId && fallback.find(t => t.TemplateId === lastSelectedTemplateId)
          ? lastSelectedTemplateId
          : (isInitialLoad && fallback.length > 0 ? fallback[0].TemplateId : null)

        const fallbackTemplate = fallbackSelectedId ? fallback.find(t => t.TemplateId === fallbackSelectedId) : null
        this.setData({
          templates: fallback,
          selectedTemplateId: fallbackSelectedId,
          currentTemplateName: fallbackTemplate ? fallbackTemplate.TemplateName : '',
          globalSelectedTemplateId: fallbackSelectedId || this.data.globalSelectedTemplateId,
          globalSelectedTemplateName: fallbackTemplate ? fallbackTemplate.TemplateName : this.data.globalSelectedTemplateName,
          globalSelectedCategoryName: fallbackSelectedId ? '' : this.data.globalSelectedCategoryName,
          templatePageIndex: 0,
          selectedCategoryName: '',
          templateTotal: fallback.length
        })

        // 只有在选中了模板时才构建动态输入和生成预览
        if (fallbackSelectedId) {
          this.buildDynamicInputsFromTemplate()
          this.generatePreview()
        }
        this.initTemplateScrollState(false)
      }
    }
  },

    /**
   * 初始化分类并加载模板：
   * - 若获取到分类：构建标签页，并加载第一个分类
   * - 若无分类：按分类名为空加载
   * - 优先恢复到最后使用的分类和模板位置
   */
  initTemplateCategoriesAndLoad: async function () {
    try {
      const categories = await templateService.getTemplateCategories()
      const categoryList = Array.isArray(categories) ? categories : []

      // 获取最后使用的状态
      const lastUsedState = this.getLastUsedTemplateState()

      if (categoryList.length > 0) {
        // 确定要加载的分类
        let targetCategoryName = categoryList[0].categoryName || ''

        // 如果有最后使用的记录，且该分类仍然存在，则使用记录的分类
        if (lastUsedState && lastUsedState.categoryName) {
          const categoryExists = categoryList.some(cat => cat.categoryName === lastUsedState.categoryName)
          if (categoryExists) {
            targetCategoryName = lastUsedState.categoryName
          }
        }

        this.setData({ categoryList, selectedCategoryName: targetCategoryName })
        await this.loadTemplates(targetCategoryName, 0, false, true)
      } else {
        this.setData({ categoryList: [], selectedCategoryName: '' })
        await this.loadTemplates('', 0, false, true)
      }
    } catch (e) {
      console.warn('获取模板分类失败，按无分类处理:', e)
      this.setData({ categoryList: [], selectedCategoryName: '' })
      await this.loadTemplates('', 0, false, true)
    }
  },

  /**
   * 切换分类标签
   */
  /** 保存全局选中的模板状态 */
  saveLastSelectedTemplateId(categoryName, templateId) {
    // 只保存全局的最后使用状态，不保存分类记忆
    wx.setStorageSync('lastUsedTemplateState', {
      categoryName: categoryName,
      templateId: templateId,
      timestamp: Date.now()
    })
  },

  /** 获取最后使用的模板状态 */
  getLastUsedTemplateState() {
    return wx.getStorageSync('lastUsedTemplateState') || null
  },

  /** 跳转到当前选中的模板位置 */
  async jumpToCurrentTemplate() {
    const globalTemplateId = this.data.globalSelectedTemplateId
    const globalCategoryName = this.data.globalSelectedCategoryName

    if (!globalTemplateId) {
      wx.showToast({
        title: '没有选中的模板',
        icon: 'none'
      })
      return
    }

    // 如果全局选中的模板在当前分类中，直接滚动
    const templateIndex = this.findTemplateIndexById(globalTemplateId)
    if (templateIndex >= 0) {
      this.setData({
        scrollIntoViewId: `template-${templateIndex}`
      })
      this.showScrollHint('已定位到当前选中模板')
      return
    }

    // 如果不在当前分类，需要切换到对应分类
    if (globalCategoryName !== this.data.selectedCategoryName) {
      try {
        // 切换到对应分类
        this.setData({ selectedCategoryName: globalCategoryName, templatePageIndex: 0 })
        await this.loadTemplates(globalCategoryName, 0, false, false)

        // 切换后再次尝试定位
        setTimeout(() => {
          const newTemplateIndex = this.findTemplateIndexById(globalTemplateId)
          if (newTemplateIndex >= 0) {
            this.setData({
              scrollIntoViewId: `template-${newTemplateIndex}`
            })
            this.showScrollHint('已切换分类并定位到选中模板')
          }
        }, 300)
      } catch (e) {
        console.warn('切换分类失败:', e)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    }
  },

  /** 切换分类标签 */
  selectCategory: async function (e) {
    const categoryName = e.currentTarget.dataset.name || ''
    if (categoryName === this.data.selectedCategoryName) return

    // 切换分类时不保存状态，避免冲突
    this.setData({ selectedCategoryName: categoryName, templatePageIndex: 0 })
    await this.loadTemplates(categoryName, 0, false, false)
  },






  /**
   * 基于当前模板构建动态输入项 dynamicInputs
   */
  buildDynamicInputsFromTemplate: function () {
    const template = this.getCurrentTemplate()
    if (!template || !Array.isArray(template.DrawObjects)) {
      // 没有选中模板时，不做任何操作（保持当前动态输入状态）
      return
    }
    // 读取已保存的输入值
    const saved = wx.getStorageSync(`dynamicInputs:${template.TemplateName}`) || {}

    const inputs = template.DrawObjects.map((obj, idx) => {
      if (!obj || !obj.IsInput) return null
      const format = obj.InputFormat || 'TEXT'
      const title = obj.InputTitle || obj.Content || ''
      const max = obj.InputMax || null
      const regex = obj.InputRegex || ''

      const savedItem = saved[idx] || {}
      const item = { drawIndex: idx, title, format, max, regex, value: '', charCount: 0 }

      if (format === 'TEXT') {
        item.value = savedItem.value || ''
        item.charCount = item.value.length
      } else if (format === 'DATE') {
        item.date = savedItem.date || ''
      } else if (format === 'DATE_TIME') {
        item.date = savedItem.date || ''
        item.time = savedItem.time || ''
        item.seconds = savedItem.seconds || '00'
        // 解析时间为时分秒索引，用于多列选择器
        if (item.time) {
          const timeParts = item.time.split(':')
          item.timeHour = parseInt(timeParts[0]) || 0
          item.timeMinute = parseInt(timeParts[1]) || 0
        } else {
          item.timeHour = 0
          item.timeMinute = 0
        }
        item.timeSecond = parseInt(item.seconds) || 0
      } else if (format === 'CHECK') {
        // 解析选项：形如 "|早|午|晚"
        const labels = (regex || '').split('|').filter(x => !!x)
        const selectedSet = new Set(Array.isArray(savedItem.selected) ? savedItem.selected : [])
        item.options = labels.map(l => ({ label: l, checked: selectedSet.has(l) }))
        item.selected = Array.from(selectedSet)
      } else if (format === 'OPTION') {
        // 解析选项：形如 "|选项1|选项2|选项3"
        const labels = (regex || '').split('|').filter(x => !!x)
        // 添加一个空选项作为第一项，允许用户不选择
        item.options = [{ label: '请选择', value: '' }, ...labels.map(l => ({ label: l, value: l }))]

        // 如果InputMax > 0，添加"其他"选项
        if (max > 0) {
          item.options.push({ label: '其他', value: 'OTHER' })
        }

        item.selectedValue = savedItem.selectedValue || '' // 单选值，允许为空
        item.customValue = savedItem.customValue || '其他' // 自定义输入值，默认为"其他"
        item.isCustomSelected = savedItem.isCustomSelected || false // 是否选择了"其他"选项

        // 计算当前选中项的索引，用于picker组件
        if (item.isCustomSelected) {
          // 如果选择的是"其他"，索引为最后一个
          item.selectedIndex = item.options.length - 1
        } else if (item.selectedValue) {
          // 如果选择的是预设选项，找到对应索引
          item.selectedIndex = labels.indexOf(item.selectedValue) + 1
        } else {
          // 默认选择"请选择"
          item.selectedIndex = 0
        }
      }
      return item
    }).filter(Boolean)

    this.setData({ dynamicInputs: inputs })
  },

  /**
   * 将 dynamicInputs 的值写入模板 DrawObjects 内容
   * - TEXT: 直接赋值（正则与长度限制仅用于前端校验）
   * - DATE: YYYY-MM-DD
   * - DATE_TIME: YYYY-MM-DD HH:mm:ss
   * - CHECK: 将 "|" 选项转换为对应的 ☐/☑ 序列
   */
  applyDynamicInputsToTemplate(tpl) {
    if (!tpl || !Array.isArray(tpl.DrawObjects)) return

    // 构建一个索引->输入项的映射
    const inputMap = {}
    ;(this.data.dynamicInputs || []).forEach(item => {
      inputMap[item.drawIndex] = item
    })

    tpl.DrawObjects.forEach((obj, idx) => {
      if (!obj || !obj.IsInput) return
      const item = inputMap[idx]
      if (!item) return
      const fmt = item.format
      if (fmt === 'TEXT') {
        obj.Content = item.value || ''
      } else if (fmt === 'DATE') {
        obj.Content = item.date || ''
      } else if (fmt === 'DATE_TIME') {
        // 允许日期和时间单独输出
        if (item.date && item.time) {
          // 日期和时间都有
          const ss = item.seconds || '00'
          obj.Content = `${item.date} ${item.time}:${ss}`
        } else if (item.date) {
          // 只有日期
          obj.Content = item.date
        } else if (item.time) {
          // 只有时间
          const ss = item.seconds || '00'
          obj.Content = `${item.time}:${ss}`
        } else {
          obj.Content = ''
        }
      } else if (fmt === 'CHECK') {
        const options = item.options || [] // [{label, checked}]
        const parts = options.map(opt => `${opt.checked ? '☑' : '☐'}${opt.label}`)
        obj.Content = parts.join('')
      } else if (fmt === 'OPTION') {
        // OPTION类型：如果选择了"其他"，显示自定义输入值；否则显示选中的选项
        if (item.isCustomSelected) {
          obj.Content = item.customValue || ''
        } else {
          obj.Content = item.selectedValue || ''
        }
      }
    })
  },

  /**
   * 事件 - 动态 TEXT 输入
   */
  onDynamicTextInput(e) {
    const idx = e.currentTarget.dataset.index
    const val = e.detail.value
    const item = this.data.dynamicInputs[idx]
    if (!item) return

    // 长度限制
    const max = item.max || 0
    let newVal = val
    if (max > 0) newVal = newVal.slice(0, max)

    // 正则限制（如有）
    if (item.regex) {
      try {
        const re = new RegExp(item.regex)
        if (!re.test(newVal)) {
          // 不合规则回退到上一次有效值
          newVal = item.value || ''
        }
      } catch (err) {
        console.warn('无效的 InputRegex:', item.regex)
      }
    }

    const key = `dynamicInputs[${idx}].value`
    this.setData({ [key]: newVal })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 DATE 选择
   */
  onDynamicDateChange(e) {
    const idx = e.currentTarget.dataset.index
    const val = e.detail.value
    const key = `dynamicInputs[${idx}].date`
    this.setData({ [key]: val })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  onDynamicSetToday(e) {
    const idx = e.currentTarget.dataset.index
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`
    const key = `dynamicInputs[${idx}].date`
    this.setData({ [key]: dateStr })
    this.saveDynamicInputs()
    this.generatePreview()
  },



  onDynamicClearDate(e) {
    const idx = e.currentTarget.dataset.index
    const key = `dynamicInputs[${idx}].date`
    this.setData({ [key]: '' })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 DATE_TIME 时间/秒
   */
  onDynamicTimeChange(e) {
    const idx = e.currentTarget.dataset.index
    const val = e.detail.value
    const key = `dynamicInputs[${idx}].time`
    this.setData({ [key]: val })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  onDynamicSecondInput(e) {
    const idx = e.currentTarget.dataset.index
    let val = e.detail.value
    // 仅两位秒，0-59
    val = (val || '').replace(/[^0-9]/g, '').slice(0, 2)
    const n = parseInt(val, 10)
    if (!isNaN(n)) {
      val = String(Math.max(0, Math.min(59, n))).padStart(2, '0')
    } else {
      val = ''
    }
    const key = `dynamicInputs[${idx}].seconds`
    this.setData({ [key]: val })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  onDynamicClearDateTime(e) {
    const idx = e.currentTarget.dataset.index
    this.setData({ [`dynamicInputs[${idx}].date`]: '', [`dynamicInputs[${idx}].time`]: '', [`dynamicInputs[${idx}].seconds`]: '00' })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 DATE_TIME 设置今天日期
   */
  onDynamicSetTodayDateTime(e) {
    const idx = e.currentTarget.dataset.index
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`
    const key = `dynamicInputs[${idx}].date`
    this.setData({ [key]: dateStr })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 DATE_TIME 设置现在时间
   */
  onDynamicSetNowDateTime(e) {
    const idx = e.currentTarget.dataset.index
    const now = new Date()
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`
    const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`
    const secondsStr = String(now.getSeconds()).padStart(2, '0')

    this.setData({
      [`dynamicInputs[${idx}].date`]: dateStr,
      [`dynamicInputs[${idx}].time`]: timeStr,
      [`dynamicInputs[${idx}].seconds`]: secondsStr,
      [`dynamicInputs[${idx}].timeHour`]: now.getHours(),
      [`dynamicInputs[${idx}].timeMinute`]: now.getMinutes(),
      [`dynamicInputs[${idx}].timeSecond`]: now.getSeconds()
    })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 DATE_TIME 多列时间选择器变化
   */
  onDynamicTimeMultiChange(e) {
    const idx = e.currentTarget.dataset.index
    const values = e.detail.value
    const hour = parseInt(values[0]) || 0
    const minute = parseInt(values[1]) || 0
    const second = parseInt(values[2]) || 0

    const timeStr = `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`
    const secondsStr = String(second).padStart(2, '0')

    this.setData({
      [`dynamicInputs[${idx}].time`]: timeStr,
      [`dynamicInputs[${idx}].seconds`]: secondsStr,
      [`dynamicInputs[${idx}].timeHour`]: hour,
      [`dynamicInputs[${idx}].timeMinute`]: minute,
      [`dynamicInputs[${idx}].timeSecond`]: second
    })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 DATE_TIME 清空时间
   */
  onDynamicClearTime(e) {
    const idx = e.currentTarget.dataset.index
    this.setData({
      [`dynamicInputs[${idx}].time`]: '',
      [`dynamicInputs[${idx}].seconds`]: '00',
      [`dynamicInputs[${idx}].timeHour`]: 0,
      [`dynamicInputs[${idx}].timeMinute`]: 0,
      [`dynamicInputs[${idx}].timeSecond`]: 0
    })
    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 CHECK 勾选切换
   */
  onDynamicCheckToggle(e) {
    const idx = e.currentTarget.dataset.idx // 使用 drawIndex，避免列表下标歧义
    const optLabel = e.currentTarget.dataset.option
    // 找到 dynamicInputs 中 drawIndex == idx 的项
    const list = this.data.dynamicInputs || []
    const realIndex = list.findIndex(it => String(it.drawIndex) === String(idx))
    if (realIndex < 0) return
    const item = list[realIndex]

    // 更新 options[].checked
    const options = (item.options || []).map(o => o.label === optLabel ? { ...o, checked: !o.checked } : o)

    // 同步 selected 便于持久化
    const selected = options.filter(o => o.checked).map(o => o.label)

    this.setData({
      [`dynamicInputs[${realIndex}].options`]: options,
      [`dynamicInputs[${realIndex}].selected`]: selected,
    })

    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 OPTION 选择切换
   */
  onDynamicOptionSelect(e) {
    const idx = e.currentTarget.dataset.idx // 使用 drawIndex
    const selectedIndex = parseInt(e.detail.value) // picker返回的是索引

    // 找到 dynamicInputs 中 drawIndex == idx 的项
    const list = this.data.dynamicInputs || []
    const realIndex = list.findIndex(it => String(it.drawIndex) === String(idx))
    if (realIndex < 0) return

    const item = list[realIndex]
    const selectedOption = (item.options && item.options[selectedIndex]) ? item.options[selectedIndex] : null
    const selectedValue = selectedOption ? selectedOption.value : ''
    const isCustomSelected = selectedValue === 'OTHER'

    this.setData({
      [`dynamicInputs[${realIndex}].selectedIndex`]: selectedIndex,
      [`dynamicInputs[${realIndex}].selectedValue`]: selectedValue,
      [`dynamicInputs[${realIndex}].isCustomSelected`]: isCustomSelected,
    })

    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 事件 - 动态 OPTION 自定义输入
   */
  onDynamicOptionCustomInput(e) {
    const idx = e.currentTarget.dataset.idx // 使用 drawIndex
    const val = e.detail.value

    // 找到 dynamicInputs 中 drawIndex == idx 的项
    const list = this.data.dynamicInputs || []
    const realIndex = list.findIndex(it => String(it.drawIndex) === String(idx))
    if (realIndex < 0) return

    const item = list[realIndex]

    // 长度限制
    const max = item.max || 0
    let newVal = val
    if (max > 0) newVal = newVal.slice(0, max)

    this.setData({
      [`dynamicInputs[${realIndex}].customValue`]: newVal,
    })

    this.saveDynamicInputs()
    this.generatePreview()
  },

  /**
   * 存储 dynamicInputs
   */
  saveDynamicInputs() {
    const template = this.getCurrentTemplate()
    if (!template) return
    const toSave = {}
    ;(this.data.dynamicInputs || []).forEach(item => {
      toSave[item.drawIndex] = item
    })
    wx.setStorageSync(`dynamicInputs:${template.TemplateName}`, toSave)
  },

  /**
   * 尝试连接上次使用的打印机
   */
  tryConnectLastPrinter() {
    const lastPrinter = wx.getStorageSync('lastConnectedPrinter')
    if (lastPrinter) {
      this.setData({
        printerDeviceSn: lastPrinter.name || lastPrinter.deviceId
      })
      // 这里可以尝试连接上次的打印机
      // bleTool.connectBleDevice(lastPrinter)...
      this.connectDevice(lastPrinter)
    }
  },
  /**
   * 弹窗：暂不连接
   */
  skipConnectPrompt() {
    this.setData({ showConnectPrompt: false })
  },

  /**
   * 弹窗：去连接
   */
  confirmConnectPrompt() {
    this.setData({ showConnectPrompt: false })
    this.startConnectPrinter()
  },


  /**
   * 选择模版
   */
  selectTemplate(e) {
    const index = e.currentTarget.dataset.index
    const template = this.data.templates[index]
    if (!template) return

    this.setData({
      selectedTemplateId: template.TemplateId,
      currentTemplateName: template.TemplateName,
      globalSelectedTemplateId: template.TemplateId,
      globalSelectedTemplateName: template.TemplateName,
      globalSelectedCategoryName: this.data.selectedCategoryName || '',
      scrollIntoViewId: `template-${index}`
    })

    // 保存选择到本地存储（按分类记忆）
    this.saveLastSelectedTemplateId(this.data.selectedCategoryName || '', template.TemplateId)

    // 基于模板构建动态输入
    this.buildDynamicInputsFromTemplate()

    // 检查耗材规格匹配
    // if (this.data.printerStatus === 'connected') {
    //   this.checkMaterialCompatibility()
    // }

    // 重新生成预览图
    this.generatePreview()
  },

  /**
   * 模版列表滚动事件
   */
  onTemplateScroll(e) {
    if (this.data.templateScrollState.scrollProtection) return;

    const { scrollLeft, scrollWidth } = e.detail;
    const { containerWidth } = this.data.templateScrollState;
    const rightBoundary = Math.max(0, scrollWidth - containerWidth);

    // 更新左右可滚动状态
    const canScrollLeft = scrollLeft > 10;
    const canScrollRight = scrollLeft < rightBoundary - 10;
    this.setData({
      'templateScrollState.canScrollLeft': canScrollLeft,
      'templateScrollState.canScrollRight': canScrollRight
    });

    // 预取：当接近右边界 N 个卡片宽度时，尝试触发下一页加载
    const rpx2px = containerWidth / 750;
    const itemWidthPx = (this.data.templateItemRpxTotal || 260) * rpx2px;
    const thresholdPx = (this.data.nearEndPrefetchCount || 5) * itemWidthPx;
    const remainingPx = rightBoundary - scrollLeft;

    if (remainingPx <= thresholdPx) {
      const { selectedCategoryName, templatePageIndex, isLoadingTemplates, templateTotal, templates } = this.data
      // 判断是否还有更多数据
      if (!isLoadingTemplates && typeof templateTotal === 'number' && Array.isArray(templates) && templates.length < templateTotal) {
        if (this._loadingNextPage) return
        this._loadingNextPage = true
        this.setData({ isLoadingTemplates: true })
        const nextPage = (templatePageIndex || 0) + 1
        this.loadTemplates(selectedCategoryName || '', nextPage, true, false)
          .finally(() => {
            this._loadingNextPage = false
            this.setData({ isLoadingTemplates: false })
          })
      }
    }
  },

  /**
   * 模版列表滚动到左边界
   */
  onTemplateScrollToUpper() {
    // this.showScrollHint('已到达列表开头')
    // this.setData({
    //   'templateScrollState.canScrollLeft': false
    // })
    // this.data.templateScrollState.scrollProtection = true;
    // setTimeout(() => {
    //   this.data.templateScrollState.scrollProtection = false;
    // }, 500);
  },

  /**
   * 模版列表滚动到右边界
   */
  onTemplateScrollToLower() {
    // 仍保留作为兜底，但增加是否还有更多数据的判断
    const { categoryList, selectedCategoryName, templatePageIndex, isLoadingTemplates, templateTotal, templates } = this.data
    if (!categoryList || categoryList.length === 0) return
    if (isLoadingTemplates) return
    if (typeof templateTotal === 'number' && Array.isArray(templates) && templates.length >= templateTotal) return

    this.setData({ isLoadingTemplates: true })
    const nextPage = (templatePageIndex || 0) + 1
    this.loadTemplates(selectedCategoryName || '', nextPage, true, false)
      .finally(() => {
        this.setData({ isLoadingTemplates: false })
      })
  },

  /**
   * 显示滚动提示
   */
  showScrollHint(text) {
    // 清除之前的定时器
    if (this.data.templateScrollState.hintTimer) {
      clearTimeout(this.data.templateScrollState.hintTimer)
    }

    this.setData({
      'templateScrollState.showHint': true,
      'templateScrollState.hintText': text
    })

    // 2秒后隐藏提示
    const timer = setTimeout(() => {
      this.setData({
        'templateScrollState.showHint': false,
        'templateScrollState.hintText': ''
      })
    }, 2000)

    this.setData({
      'templateScrollState.hintTimer': timer
    })
  },

  /**
   * 初始化模版滚动状态
   * @param {boolean} resetToTop 是否重置到顶部（用于切换分类）
   */
  initTemplateScrollState(resetToTop = false) {
    // 延迟获取滚动视图信息，确保DOM已渲染
    setTimeout(() => {
      if (resetToTop) {
        // 切换分类时，先重置到顶部，然后再定位到选中项
        this.setData({
          scrollIntoViewId: 'template-0'
        })

        // 再延迟一点时间定位到选中项
        setTimeout(() => {
          const templateIndex = this.findTemplateIndexById(this.data.selectedTemplateId)
          if (templateIndex > 0) {
            this.setData({
              scrollIntoViewId: `template-${templateIndex}`
            })
          }
        }, 100)
      } else {
        // 正常情况下，如果有选中的模板，滚动到该位置
        const templateIndex = this.findTemplateIndexById(this.data.selectedTemplateId)
        if (templateIndex > 0) {
          this.setData({
            scrollIntoViewId: `template-${templateIndex}`
          })
        }
      }

      const query = wx.createSelectorQuery().in(this)
      query.select('.template-list').scrollOffset()
      query.select('.template-list').boundingClientRect()
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const scrollInfo = res[0]
          const rectInfo = res[1]
          const { scrollLeft, scrollWidth } = scrollInfo
          const { width: scrollViewWidth } = rectInfo

          const canScrollLeft = scrollLeft > 0
          const canScrollRight = scrollLeft < scrollWidth - scrollViewWidth - 1

          this.setData({
            'templateScrollState.canScrollLeft': canScrollLeft,
            'templateScrollState.canScrollRight': canScrollRight
          })

          console.log('初始化滚动状态:', {
            scrollLeft,
            scrollWidth,
            scrollViewWidth,
            canScrollLeft,
            canScrollRight,
            resetToTop
          })
        }
      })
    }, 500)
  },

  /**
   * 生成预览图
   */
  generatePreview() {
    const template = this.getCurrentTemplate()
    if (!template) {
      // 没有选中模板时，不做任何操作（保持当前预览状态）
      return
    }

    // 如果正在生成预览图，跳过本次请求
    if (this.data.previewGenerating) {
      console.log('预览图正在生成中，跳过本次请求')
      return
    }

    // 生成唯一的预览ID，用于防止竞态条件
    const previewId = ++this.data.currentPreviewId

    // 设置生成状态
    this.setData({
      previewGenerating: true
    })

    // 创建临时模版对象，更新内容
    const tempTemplate = JSON.parse(JSON.stringify(template))
    tempTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 使用动态输入，填充 DrawObjects 中 IsInput 的节点内容
    this.applyDynamicInputsToTemplate(tempTemplate)

    const that = this
    bleToothManage.doDrawPreview(this.data.canvasText, [tempTemplate], this.data.canvasBarCode, res => {
      // 检查是否为最新的预览请求，防止旧请求覆盖新请求的结果
      if (previewId !== that.data.currentPreviewId) {
        console.log(`预览图请求已过期，忽略结果。当前ID: ${that.data.currentPreviewId}, 请求ID: ${previewId}`)
        return
      }

      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        let resultValue = res.ResultValue
        that.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
      } else if (res.ResultCode == constants.globalResultCode.ResultCodeSuccess) {
        if (res.ResultValue && res.ResultValue.previewList && res.ResultValue.previewList.length > 0) {
          that.setData({
            previewImagePath: res.ResultValue.previewList[0]
          })
        }
      }

      // 重置生成状态
      that.setData({
        previewGenerating: false
      })
    }).catch(error => {
      console.error('生成预览图失败:', error)
      // 重置生成状态
      that.setData({
        previewGenerating: false
      })
    })
  },

  /**
   * 保存标签内容到本地存储
   */
  saveLabelContent() {
    wx.setStorageSync('lastLabelContent', this.data.labelContent)
  },

  /**
   * 获取耗材信息
   */
  getMaterialInfo() {
    const that = this
    return new Promise((resolve, reject) => {
      console.log('开始获取耗材信息...')
      bleToothManage.ConsumableInformation().then(res => {
        console.log('耗材信息API响应:', res)
        if (res && res.ResultCode === 0) {
          // 获取成功
          const materialInfo = res.ResultValue
          console.log('耗材信息获取成功:', materialInfo)
          that.setData({
            materialInfo: materialInfo
          })
          resolve(materialInfo)
        } else {
          // 获取失败
          console.error('获取耗材信息失败 - ResultCode:', res?.ResultCode, 'Response:', res)
          reject(res)
        }
      }).catch(error => {
        console.error('获取耗材信息异常:', error)
        reject(error)
      })
    })
  },

  /**
   * 检查耗材规格兼容性
   * @param {boolean} forceRefresh - 是否强制重新获取耗材信息，默认false使用缓存
   */
  checkMaterialCompatibility(forceRefresh = false) {
    if (!this.data.materialInfo || forceRefresh) {
      // 如果没有耗材信息或强制刷新，先获取最新信息
      console.log('重新获取耗材信息进行兼容性检查...')
      this.getMaterialInfo().then(() => {
        this.performCompatibilityCheck()
      }).catch(error => {
        console.error('无法获取耗材信息进行兼容性检查:', error)
      })
    } else {
      console.log('使用缓存的耗材信息进行兼容性检查')
      this.performCompatibilityCheck()
    }
  },

  /**
   * 执行兼容性检查
   */
  performCompatibilityCheck() {
    const template = this.getCurrentTemplate()
    const materialInfo = this.data.materialInfo

    console.log('执行兼容性检查:', {
      hasTemplate: !!template,
      hasMaterialInfo: !!materialInfo,
      template: template ? {
        name: template.TemplateName,
        width: template.Width,
        height: template.Height,
        gap: template.Gap
      } : null,
      materialInfo: materialInfo
    })

    if (!template || !materialInfo) {
      console.log('兼容性检查跳过：缺少模板或耗材信息')
      return
    }

    // 比较耗材规格与模版规格
    // paperDirectionSize 对应 Height
    // printHeadDirectionSize 对应 Width
    // gap 对应 Gap
    const heightMatch = materialInfo.paperDirectionSize === template.Height
    const widthMatch = materialInfo.printHeadDirectionSize === template.Width
    const gapMatch = materialInfo.gap === template.Gap

    const isCompatible = heightMatch && widthMatch && gapMatch

    console.log('兼容性检查结果:', {
      template: {
        width: template.Width,
        height: template.Height,
        gap: template.Gap
      },
      material: {
        width: materialInfo.printHeadDirectionSize,
        height: materialInfo.paperDirectionSize,
        gap: materialInfo.gap
      },
      match: {
        heightMatch,
        widthMatch,
        gapMatch
      },
      isCompatible: isCompatible
    })

    this.setData({
      materialMismatch: !isCompatible
    })

    if (!isCompatible) {
      // 更新状态栏显示不匹配信息
      this.setData({
        printerErrorMessage: '当前耗材规格与模版不一致',
        printerErrorCode: null
      })

      console.log('⚠️ 耗材规格不匹配 - 已设置 materialMismatch = true')
    } else {
      // 规格匹配，清除错误信息
      if (this.data.printerErrorMessage === '当前耗材规格与模版不一致') {
        this.setData({
          printerErrorMessage: '',
          printerErrorCode: null
        })
      }
      console.log('✅ 耗材规格匹配 - 已设置 materialMismatch = false')
    }
  },



  /**
   * 处理打印机状态码
   */
  handlePrinterStatus(resultCode, useUserFriendlyMessage = false) {
    const message = getStatusMessage(resultCode, useUserFriendlyMessage);
    const category = getStatusCategory(resultCode);
    const errorLevel = getErrorLevel(resultCode);
    const isSuccess = isSuccessStatus(resultCode);

    // 成功状态码 - 设备连接正常
    if (isSuccess) {
      return {
        status: 'connected',
        message: message,
        isConnected: true,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 硬件/耗材相关错误 - 设备已连接但打印有问题
    if (category === 'hardware') {
      return {
        status: 'connected', // 设备仍然连接，只是打印有问题
        message: message,
        isPrintError: true,
        isConnected: true,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 蓝牙连接相关错误 - 设备连接问题
    if (category === 'bluetooth') {
      return {
        status: 'error',
        message: message,
        isConnected: false,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 数据处理错误 - 根据具体情况判断
    if (category === 'dataProcessing') {
      // 某些数据处理错误不影响连接状态
      const nonConnectionErrors = [108, 116, 118, 119, 120, 121, 122, 123, 124, 135];
      const isConnected = nonConnectionErrors.includes(resultCode);

      return {
        status: isConnected ? 'connected' : 'error',
        message: message,
        isConnected: isConnected,
        isPrintError: isConnected,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 其他未知错误
    return {
      status: 'error',
      message: message,
      isConnected: false,
      category: 'unknown',
      errorLevel: 'error'
    }
  },

  /**
   * 更新打印机状态
   */
  updatePrinterStatus(resultCode, customMessage = '', useUserFriendlyMessage = false) {
    const statusInfo = this.handlePrinterStatus(resultCode, useUserFriendlyMessage)

    this.setData({
      printerStatus: statusInfo.status,
      printerErrorCode: resultCode,
      printerErrorMessage: customMessage || statusInfo.message
    })
    // 更新状态帮助提示的可见性与图片
    this.computeStatusHelpTip()

    // 如果设备仍然连接，保持设备信息
    if (statusInfo.isConnected && this.data.printerDeviceSn) {
      // 保持设备连接信息不变
    } else if (!statusInfo.isConnected) {
      // 设备断开连接，清除设备信息
      this.setData({
        printerDeviceSn: ''
      })
    }
  },

	

	  /**
	   * 计算并更新状态帮助提示的可见性与图片
	   */
	  computeStatusHelpTip() {
	    const ctx = {
	      code: this.data.printerErrorCode,
	      status: this.data.printerStatus,
	      message: this.data.printerErrorMessage
	    }
	    const tip = getHelpTipByContext(ctx)
	    this.setData({
	      statusHelpTipAvailable: !!tip,
	      statusHelpImageUrl: tip ? (tip.image || '') : ''
	    })
	  },

	  showStatusHelpTip() {
	    if (!this.data.statusHelpTipAvailable) return
	    this.setData({
	      showStatusHelpModal: true,
	      helpImageScrollable: false,
	      helpImageScrollIndicatorOpacity: 0
	    })
	  },

	  closeStatusHelpTip() {
	    this.setData({
	      showStatusHelpModal: false,
	      helpImageScrollable: false,
	      helpImageScrollIndicatorOpacity: 0
	    })
	  },

  /**
   * 显示用户友好的错误提示
   */
  showUserFriendlyError(resultCode, title = '操作失败') {
    const userFriendlyMessage = getStatusMessage(resultCode, true);
    const errorLevel = getErrorLevel(resultCode);

    // 根据错误级别选择不同的显示方式
    if (errorLevel === 'warning') {
      wx.showModal({
        title: '提示',
        content: userFriendlyMessage,
        showCancel: false,
        confirmText: '知道了'
      });
    } else if (errorLevel === 'error') {
      wx.showModal({
        title: title,
        content: userFriendlyMessage,
        showCancel: false,
        confirmText: '确定'
      });
    } else {
      wx.showToast({
        title: userFriendlyMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 输入品名
   */
  onProductNameInput(e) {
    const value = e.detail.value.slice(0, 8) // 限制8个字符
    this.setData({
      'labelContent.productName': value,
      'inputCharCount.productName': value.length
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入操作人
   */
  onOperatorInput(e) {
    const value = e.detail.value.slice(0, 8) // 限制8个字符
    this.setData({
      'labelContent.operator': value,
      'inputCharCount.operator': value.length
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 选择日期
   */
  onDateChange(e) {
    this.setData({
      'labelContent.date': e.detail.value
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 清空日期
   */
  clearDate() {
    this.setData({
      'labelContent.date': ''
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 设置为今日日期
   */
  setToday() {
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`
    this.setData({
      'labelContent.date': dateStr
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入打印份数
   */
  onCopiesInput(e) {
    let value = e.detail.value
    // 允许空值，用户可能正在删除内容
    if (value === '') {
      this.setData({
        'labelContent.copies': ''
      })
      return
    }

    let copies = parseInt(value) || 1
    copies = Math.max(1, Math.min(250, copies)) // 限制1-250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 增加打印份数
   */
  increaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.min(250, copies + 1) // 最大250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 减少打印份数
   */
  decreaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.max(1, copies - 1) // 最小1
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 连接打印机/打印标签/停止打印按钮
   */
  onPrintAction() {
    // 防止重复操作
    if (this.data.isConnecting || this.data.isScanning || this.data.printerStatus === 'disconnecting') {
      let message = '操作进行中...'
      if (this.data.isScanning) {
        message = '正在搜索设备...'
      } else if (this.data.isConnecting) {
        message = '正在连接中...'
      } else if (this.data.printerStatus === 'disconnecting') {
        message = '正在断开连接...'
      }
      wx.showToast({
        title: message,
        icon: 'none'
      })
      return
    }

    // 未连接时，打印按钮置灰且阻止操作
    if (this.data.printerStatus !== 'connected' && !this.data.isPrinting) {
      wx.showToast({
        title: '请先连接打印机',
        icon: 'none'
      })
      this.setData({ showConnectPrompt: true })
      return
    }

    if (this.data.isPrinting) {
      // 正在打印，停止打印
      this.stopPrint()
    } else if (this.data.printerStatus === 'connected') {
      // 已连接，执行打印
      this.printLabel()
      // this.printLabelByPerview()
    } else {
      // 未连接，开始连接流程
      this.startConnectPrinter()
    }
  },

  /**
   * 开始连接打印机流程
   */
  startConnectPrinter() {
    // 微信小程序示例
    wx.getSetting({
      success:(res) => {
        if (!res.authSetting['scope.userLocation']) { // Android位置权限
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              // 授权成功后打开蓝牙
              this.scanAndConnectPrinter()
            },
            fail : (err) => {
              console.log('位置权限被拒绝：', err);
              // 引导用户手动授权
              wx.showModal({
                title: '请求授权',
                content: '需要位置权限以使用蓝牙功能，请授权',
                success(res) {
                  if (res.confirm) {
                    wx.openSetting(); // 打开设置页让用户手动授权
                  }
                }
              });
            }
          });
        } else {
          // 已授权，直接打开蓝牙
          this.scanAndConnectPrinter()
        }
      }
    })
  },

  /**
   * 扫描并连接打印机
   */
  scanAndConnectPrinter() {
    this.setData({
      isScanning: true,
      blueList: []
    })

    const that = this
    let scanTimeout = null

    // 2秒后检查扫描结果
    scanTimeout = setTimeout(() => {
      bleTool.stopScanBleDevices()
      that.setData({ isScanning: false })

      if (that.data.blueList.length === 1) {
        // 只有一台设备，直接连接
        that.connectDevice(that.data.blueList[0])
      } else if (that.data.blueList.length > 1) {
        // 多台设备，显示选择对话框
        that.setData({ showDeviceSelector: true })
      } else {
        // 没有找到设备
        that.setData({
          printerStatus: 'error',
          printerErrorMessage: '未找到可用的打印机',
          printerErrorCode: null
        })
        that.computeStatusHelpTip()
        wx.showToast({
          title: '未找到可用的打印机',
          icon: 'none'
        })
      }
    }, 2000)

    // 开始扫描
    bleTool.scanBleDeviceList((res) => {
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          that.data.blueList.push(res.ResultValue.devices[0])
          that.setData({
            blueList: that.data.blueList
          })
        }
      }
    }).catch(error => {
      console.log('搜索蓝牙设备失败:', error)
      that.setData({ isScanning: false })
      clearTimeout(scanTimeout)
    })
  },



  /**
   * 连接指定设备
   */
  connectDevice(device) {
    // 设置连接中状态
    const deviceId = device.name || device.deviceId;
    this.setData({
      isConnecting: true,
      printerStatus: 'connecting',
      printerErrorCode: null,
      printerErrorMessage: '',
      connectingDeviceId: deviceId
    })

    const that = this

    // 根据checkPrinterBinding配置决定是否需要检查设备
    if (!this.data.checkPrinterBinding) {
      // 不需要检查设备，直接连接
      console.log('跳过设备检查，直接连接设备')
      this.performDeviceConnection(device)
      return
    }

    // 需要检查设备是否在库
    console.log('开始检查设备是否在库...')
    checkPrinterDevice(device).then(checkResult => {
      console.log('设备检查结果:', checkResult)

      if (!checkResult.success) {
        // 设备检查失败
        let errorMessage = '设备检查失败'
        let errorCode = checkResult.code || 9000

        // 根据错误码设置具体的错误信息
        switch (errorCode) {
          case 9000: // 设备不存在
            errorMessage = '非官方指定设备'
            break
          case 9001: // 打印机型号不能为空
            errorMessage = '打印机型号错误'
            break
          case 9002: // 设备SN码不能为空
            errorMessage = '设备序列号无效'
            break
          case 401: // 鉴权失败
            errorMessage = '设备验证失败'
            break
          case 403: // 权限不足
            errorMessage = '设备访问权限不足'
            break
          case 999: // 系统繁忙
            errorMessage = '系统繁忙，请稍后再试'
            break
          case 1000: // 参数错误
            errorMessage = '设备参数错误'
            break
          default:
            errorMessage = checkResult.message || '设备检查失败'
        }

        that.setData({
          isConnecting: false,
          printerStatus: 'error',
          printerErrorCode: errorCode,
          printerErrorMessage: errorMessage
        })

        wx.showToast({
          title: `${errorMessage}，连接失败`,
          icon: 'none',
          duration: 3000
        })
        return
      }

      if (!checkResult.isInStock) {
        // 设备不在库
        that.setData({
          isConnecting: false,
          printerStatus: 'error',
          printerErrorCode: 9000,
          printerErrorMessage: '非官方指定设备'
        })

        wx.showToast({
          title: '非官方指定设备，连接失败',
          icon: 'none',
          duration: 3000
        })
        return
      }

      // 设备在库，继续连接流程
      this.performDeviceConnection(device)
    }).catch(error => {
      // 设备检查请求失败
      console.error('设备检查失败:', error)
      that.setData({
        isConnecting: false,
        printerStatus: 'error',
        printerErrorCode: 9999,
        printerErrorMessage: '设备检查失败'
      })

      wx.showToast({
        title: '设备检查失败，请检查网络连接',
        icon: 'none',
        duration: 3000
      })
    })
  },

  /**
   * 执行设备连接（不包含设备检查）
   */
  performDeviceConnection(device) {
    const that = this
    bleTool.connectBleDevice(device).then(res => {
        console.log('连接结果:', res)

        // 处理连接结果状态
        const statusInfo = that.handlePrinterStatus(res.ResultCode)

        if (statusInfo.isConnected) {
          // 设备连接成功
          that.setData({
            isConnecting: false,
            printerStatus: statusInfo.status,
            printerDeviceSn: device.name || device.deviceId,
            showDeviceSelector: false,
            printerErrorCode: statusInfo.isPrintError ? res.ResultCode : null,
            printerErrorMessage: statusInfo.isPrintError ? statusInfo.message : '',
            connectingDeviceId: '' // 清除连接中状态
          })

          // 启动在线检测与断开监听
          // 刚连接成功后给一个缓冲期，避免误判离线
          that._justConnectedTill = Date.now() + 10000
          that.startConnectionMonitoring()

          // 保存到本地存储
          wx.setStorageSync('lastConnectedPrinter', device)

          // 更新模版的DeviceSn
          const templates = that.data.templates
          templates.forEach(template => {
            template.DeviceSn = device.name || device.deviceId
          })
          that.setData({ templates })

          // 连接成功后获取耗材信息并检查兼容性
          // that.getMaterialInfo().then(() => {
          //   that.checkMaterialCompatibility()
          // }).catch(error => {
          //   console.log('获取耗材信息失败，但设备已连接:', error)
          // })

          // 重新加载分类与模版
          this.initTemplateCategoriesAndLoad()

          if (statusInfo.isPrintError) {
            wx.showToast({
              title: `设备已连接，但${statusInfo.message}`,
              icon: 'none',
              duration: 3000
            })
          } else {
            wx.showToast({
              title: '连接成功',
              icon: 'success'
            })
          }
        } else {
          // 连接失败
          that.setData({
            isConnecting: false,
            printerStatus: 'error',
            printerErrorCode: res.ResultCode,
            printerErrorMessage: statusInfo.message,
            showDeviceSelector: false,
            connectingDeviceId: '' // 清除连接中状态
          })

          // 使用用户友好的错误消息
          that.showUserFriendlyError(res.ResultCode, '连接失败')
        }
      }).catch(error => {
        console.log('连接异常:', error)
        that.setData({
          isConnecting: false,
          printerStatus: 'error',
          printerErrorCode: 109,
          printerErrorMessage: '连接蓝牙异常',
          connectingDeviceId: '' // 清除连接中状态
        })
        wx.showToast({
          title: '连接蓝牙异常',
          icon: 'error'
        })
      })
  },


  /**
   * 启动连接在线监控与断开监听
   */
  startConnectionMonitoring() {
    // 注册 BLE 连接状态变化监听（只注册一次）
    if (!this.connectionListenerRegistered && typeof wx.onBLEConnectionStateChange === 'function') {
      try {
        wx.onBLEConnectionStateChange((res) => {
          const last = wx.getStorageSync('lastConnectedPrinter') || {};
          const targetId = last.deviceId || '';
          if (targetId && res && res.deviceId === targetId && res.connected === false) {
            // 物理断开
            this.handleExternalDisconnect('设备已离线');
          }
        });
        this.connectionListenerRegistered = true;
      } catch (e) {
        console.warn('注册 onBLEConnectionStateChange 失败:', e);
      }
    }

    // 清理旧定时器
    this.stopConnectionMonitoring();

    // 启动周期性在线检查
    this.data.connectionCheckTimer = setInterval(() => {
      if (this.data.printerStatus !== 'connected') return;
      this.checkDeviceOnlineOnce().then(isOnline => {
        if (!isOnline) {
          this.handleExternalDisconnect('设备已离线');
        }
      });
    }, 5000);
  },

  /**
   * 停止连接在线监控
   */
  stopConnectionMonitoring() {
    if (this.data.connectionCheckTimer) {
      clearInterval(this.data.connectionCheckTimer);
      this.setData({ connectionCheckTimer: null });
    }
  },

  /**
   * 单次检查当前记录的设备是否仍在线
   * 返回 Promise<boolean>
   */
  checkDeviceOnlineOnce() {
    return new Promise((resolve) => {
      try {
        const last = wx.getStorageSync('lastConnectedPrinter') || {};
        const targetId = last.deviceId || '';
        const targetName = last.name || this.data.printerDeviceSn || '';

        // 刚建立连接的缓冲期内，避免误判为离线
        if (this._justConnectedTill && Date.now() < this._justConnectedTill) {
          resolve(true);
          return;
        }

        if (typeof wx.getConnectedBluetoothDevices !== 'function') {
          // API 不可用时保守返回 true，避免误判
          resolve(true);
          return;
        }

        const matchFn = (list) => {
          if (!Array.isArray(list)) return false;
          return list.some(d => (targetId && d.deviceId === targetId) || (targetName && d.name === targetName));
        };

        const fallbackByLocal = () => {
          try {
            return !!(bleTool && ((targetId && bleTool.deviceId === targetId) || (targetName && bleTool.deviceSn === targetName)));
          } catch (e) { return false; }
        };

        const tryWithFilter = () => {
          const services = Array.isArray(bleTool && bleTool.SUPPORT_SERVICE_UUID) ? bleTool.SUPPORT_SERVICE_UUID : ['FEE7','E0FF','FF00','FF0E'];
          wx.getConnectedBluetoothDevices({
            services,
            success: (res) => {
              try {
                const found = matchFn(res && res.devices);
                resolve(found);
              } catch (e) {
                resolve(fallbackByLocal());
              }
            },
            fail: () => resolve(fallbackByLocal())
          });
        };

        // 先尝试不加过滤获取已连接设备（兼容不同机型/系统）
        wx.getConnectedBluetoothDevices({
          success: (res) => {
            const found = matchFn(res && res.devices);
            if (found) {
              resolve(true);
            } else {
              tryWithFilter();
            }
          },
          fail: () => {
            tryWithFilter();
          }
        });
      } catch (e) {
        resolve(true);
      }
    });
  },

  /**
   * 处理外部（手动关机/信号丢失）导致的断开
   */
  handleExternalDisconnect(toastText = '已断开连接') {
    // 停止在线监控
    this.stopConnectionMonitoring();

    // 重置所有连接相关状态
    const templates = this.data.templates || [];
    templates.forEach(t => t.DeviceSn = '');

    this.setData({
      isConnecting: false,
      printerStatus: 'disconnected',
      printerDeviceSn: '',
      printerErrorCode: null,
      printerErrorMessage: '',
      isPrinting: false,
      materialInfo: null,
      showDeviceSelector: false,
      templates
    });

    // 清除本地存储的设备信息
    wx.removeStorageSync('lastConnectedPrinter');

    // 反馈提示
    wx.showToast({ title: toastText, icon: 'none' });

    // 延时显示连接提示
    setTimeout(() => {
      this.setData({ showConnectPrompt: true });
    }, 1500);
  },



  /**
   * 选择设备对话框中的设备选择
   */
  selectDevice(e) {
    // 如果正在连接中，不允许选择其他设备
    if (this.data.isConnecting) {
      wx.showToast({
        title: '正在连接设备，请稍候',
        icon: 'none',
        duration: 2000
      })
      return
    }
    const index = e.currentTarget.dataset.index
    const device = this.data.blueList[index]

    // 停止扫描
    if (this.data.isScanning) {
      bleTool.stopScanBleDevices()
      this.setData({
        isScanning: false
      })
    }

    // 检查是否为当前已连接设备
    const currentDeviceId = this.data.printerDeviceSn
    const selectedDeviceId = device.name || device.deviceId

    if (currentDeviceId === selectedDeviceId) {
      // 选择的是当前设备，不需要重新连接
      wx.showToast({
        title: '当前设备已连接',
        icon: 'success'
      })
      this.setData({
        showDeviceSelector: false
      })
      return
    }

    // 连接新设备
    this.connectDevice(device)
  },

  /**
   * 关闭设备选择对话框
   */
  closeDeviceSelector() {
    // 停止扫描
    if (this.data.isScanning) {
      bleTool.stopScanBleDevices()
    }

    this.setData({
      showDeviceSelector: false,
      isScanning: false
    })
  },

  /**
   * 更改设备
   */
  changeDevice() {
    // 防止重复操作
    if (this.data.isScanning || this.data.isConnecting || this.data.printerStatus === 'disconnecting') {
      wx.showToast({
        title: '操作进行中，请稍候',
        icon: 'none'
      })
      return
    }

    // 开始搜索设备并显示选择对话框
    this.setData({
      isScanning: true,
      blueList: [],
      showDeviceSelector: true
    })

    const that = this

    // 开始扫描，持续搜索直到用户手动停止
    bleTool.scanBleDeviceList((res) => {
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          const device = res.ResultValue.devices[0]
          // 检查是否已存在
          const exists = that.data.blueList.find(item =>
            (item.deviceId === device.deviceId) || (item.name === device.name)
          )
          if (!exists) {
            that.data.blueList.push(device)
            that.setData({
              blueList: that.data.blueList
            })
          }
        }
      }
    }).catch(error => {
      console.log('搜索蓝牙设备失败:', error)
      that.setData({
        isScanning: false,
        showDeviceSelector: false
      })
      wx.showToast({
        title: '搜索设备失败',
        icon: 'error'
      })
    })
  },

  /**
   * 打印标签
   */
  printLabel() {
    const template = this.getCurrentTemplate()
    if (!template) {
      this.updatePrinterStatus(116) // 模板对象不能为空
      wx.showToast({
        title: '请选择模版',
        icon: 'none'
      })
      return
    }



    // // 每次打印前都重新获取最新的耗材信息，避免缓存问题
    // console.log('打印前重新获取最新耗材信息...')
    // this.getMaterialInfo().then(() => {
    //   console.log('最新耗材信息获取完成，开始兼容性检查')
    //   this.performCompatibilityCheck()

    //   console.log('当前耗材匹配状态:', {
    //     materialMismatch: this.data.materialMismatch,
    //     forcePrint: this.data.forcePrint,
    //     materialInfo: this.data.materialInfo,
    //     template: {
    //       name: template.TemplateName,
    //       width: template.Width,
    //       height: template.Height,
    //       gap: template.Gap
    //     }
    //   })

    //   // 检查耗材规格兼容性
    //   if (this.data.materialMismatch) {
    //     // 如果设置了强制打印，弹出确认对话框
    //     if (this.data.forcePrint) {
    //       wx.showModal({
    //         title: '耗材规格不匹配',
    //         content: '当前耗材规格与模版不一致，确定要强制打印吗？',
    //         confirmText: '确定打印',
    //         cancelText: '取消',
    //         success: (res) => {
    //           if (res.confirm) {
    //             // 用户确认强制打印
    //             this.executePrint()
    //           }
    //         }
    //       })
    //       return
    //     } else {
    //       // 如果没有设置强制打印，直接阻止打印
    //       wx.showToast({
    //         title: '耗材规格不匹配，无法打印',
    //         icon: 'none',
    //         duration: 2000
    //       })
    //       return
    //     }
    //   }

    //   this.executePrint()
    // }).catch(error => {
    //   console.error('获取耗材信息失败，无法进行兼容性检查:', error)
    //   wx.showToast({
    //     title: '获取耗材信息失败，请检查设备连接',
    //     icon: 'none',
    //     duration: 2000
    //   })
    // })
    this.executePrint()
  },

  /**
   * 执行打印
   */
  executePrint() {
    // 清除可能存在的上一次打印超时定时器
    if (this.data.printTimeoutTimer) {
      clearTimeout(this.data.printTimeoutTimer)
    }

    // 设置打印状态
    this.setData({
      isPrinting: true,
      printerStatus: 'printing'
    })

    // 设置打印超时定时器（30秒）
    const timeoutTimer = setTimeout(() => {
      console.log('打印操作超时，自动重置打印状态')

      // 记录打印超时
      const template = this.getCurrentTemplate()
      if (template) {
        const printTemplate = JSON.parse(JSON.stringify(template))
        printTemplate.Copies = parseInt(this.data.labelContent.copies) || 1
        this.recordPrintResult(printTemplate, 132, '打印操作超时')
      }

      this.setData({
        isPrinting: false,
        printerStatus: 'connected',
        printerErrorCode: 132, // 使用打印异常终止的错误码
        printerErrorMessage: '打印操作超时',
        printTimeoutTimer: null
      })
      wx.showToast({
        title: '打印操作超时，已自动取消',
        icon: 'none',
        duration: 2000
      })
    }, 15000) // 15秒超时

    this.setData({
      printTimeoutTimer: timeoutTimer
    })

    const template = this.getCurrentTemplate()

    // 创建打印模版对象
    const printTemplate = JSON.parse(JSON.stringify(template))
    printTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 使用动态输入，填充 DrawObjects 中 IsInput 的节点内容
    this.applyDynamicInputsToTemplate(printTemplate)

    const that = this
    // 执行打印
    bleToothManage.doPrintMatrix(this.data.canvasText, [printTemplate], this.data.canvasBarCode, res => {
      console.log('打印回调:', res)

      // 清除打印超时定时器
      if (that.data.printTimeoutTimer) {
        clearTimeout(that.data.printTimeoutTimer)
        that.setData({
          printTimeoutTimer: null
        })
      }

      // 处理打印状态
      const statusInfo = that.handlePrinterStatus(res.ResultCode)

      // 根据 ResultCode 区分打印状态
      if (res.ResultCode === 0) {
        // ResultCode 0: 打印完成
        that.setData({
          isPrinting: false,
          printerStatus: 'connected',
          printerErrorCode: null,
          printerErrorMessage: ''
        })

        // 记录打印成功
        that.recordPrintResult(printTemplate, res.ResultCode, '打印成功')

        // 如果之前有耗材不匹配的警告，重新检查
        // if (that.data.materialMismatch) {
        //   that.checkMaterialCompatibility()
        // }

        wx.showToast({
          title: '打印成功',
          icon: 'success'
        })
      } else if (res.ResultCode === 100) {
        // ResultCode 100: 开始打印，保持打印中状态
        console.log('开始打印，保持打印中状态')
        // 不设置 isPrinting: false，保持打印中状态
      } else if (isSuccessStatus(res.ResultCode)) {
        // 其他成功状态码（1, 117等）
        that.setData({
          isPrinting: false,
          printerStatus: 'connected',
          printerErrorCode: null,
          printerErrorMessage: ''
        })

        // 记录打印成功
        that.recordPrintResult(printTemplate, res.ResultCode, '操作成功')

        wx.showToast({
          title: '操作成功',
          icon: 'success'
        })
      } else if (statusInfo.isConnected) {
        // 设备仍然连接，但打印有问题
        that.setData({
          isPrinting: false,
          printerStatus: 'connected',
          printerErrorCode: res.ResultCode,
          printerErrorMessage: statusInfo.message,
          printTimeoutTimer: null
        })

        // 记录打印失败
        that.recordPrintResult(printTemplate, res.ResultCode, statusInfo.message || '打印失败')

        // 使用用户友好的错误消息
        that.showUserFriendlyError(res.ResultCode, '打印失败')
      } else {
        // 设备连接断开
        that.setData({
          isPrinting: false,
          printerStatus: 'error',
          printerErrorCode: res.ResultCode,
          printerErrorMessage: statusInfo.message,
          printTimeoutTimer: null
        })

        // 记录打印失败
        that.recordPrintResult(printTemplate, res.ResultCode, statusInfo.message || '设备连接断开')

        // 使用用户友好的错误消息
        that.showUserFriendlyError(res.ResultCode, '打印失败')
      }
    }).catch(error => {
      console.log('打印异常:', error)

      // 清除打印超时定时器
      if (that.data.printTimeoutTimer) {
        clearTimeout(that.data.printTimeoutTimer)
      }

      // 记录打印异常
      that.recordPrintResult(printTemplate, 132, '打印异常终止')

      that.setData({
        isPrinting: false,
        printerStatus: 'error',
        printerErrorCode: 132,
        printerErrorMessage: '打印异常终止',
        printTimeoutTimer: null
      })
      wx.showToast({
        title: '打印异常终止',
        icon: 'error'
      })
    })
  },

  /**
   * 通过图片执行打印
   * @param {string} imageUrl 需要打印的图片地址（来自预览生成的图片地址）
   */
  async executePrintByImage(imageUrl) {
    console.log('图片地址:', imageUrl)
    // 校验参数与模板
    if (!imageUrl) {
      wx.showToast({ title: '图片地址无效', icon: 'none' })
      return
    }
    const template = this.getCurrentTemplate()
    if (!template) {
      this.updatePrinterStatus(116) // 模板对象不能为空
      wx.showToast({ title: '请选择模版', icon: 'none' })
      return
    }

    // 清除可能存在的上一次打印超时定时器
    if (this.data.printTimeoutTimer) {
      clearTimeout(this.data.printTimeoutTimer)
    }

    // 设置打印状态
    this.setData({
      isPrinting: true,
      printerStatus: 'printing'
    })

    // 设置打印超时定时器（30秒）
    const timeoutTimer = setTimeout(() => {
      console.log('图片打印操作超时，自动重置打印状态')

      // 记录图片打印超时
      this.recordPrintResult(template, 132, '图片打印操作超时')

      this.setData({
        isPrinting: false,
        printerStatus: 'connected',
        printerErrorCode: 132,
        printerErrorMessage: '打印操作超时',
        printTimeoutTimer: null
      })
      wx.showToast({
        title: '打印操作超时，已自动取消',
        icon: 'none',
        duration: 2000
      })
    }, 15000) // 15秒超时

    this.setData({
      printTimeoutTimer: timeoutTimer
    })

    try {
      // 如果是微信临时文件路径，需要先下载到本地
      let finalImageUrl = imageUrl
      // if (imageUrl.startsWith('wxfile://')) {
      //   console.log('检测到微信临时文件，开始下载到本地...')
      //   const downloadResult = await this.saveImageToLocal(imageUrl)
      //   finalImageUrl = downloadResult.tempFilePath
      //   console.log('下载完成，本地路径:', finalImageUrl)
      // }

      // 构建 PageImageObject（参考 index.js 的 PageImageObject 结构）
      const copies = parseInt(this.data.labelContent.copies) || 1
      const pageImageObject = {
        Width: template.Width,
        Height: template.Height,
        Rotate: template.Rotate || 0,
        Copies: copies,
        Density: template.Density || 2,
        HorizontalNum: template.HorizontalNum || 0,
        VerticalNum: template.VerticalNum || 0,
        PaperType: template.PaperType || 1,
        Gap: (template.Gap !== undefined && template.Gap !== null) ? template.Gap : 0,
        DeviceSn: template.DeviceSn || '',
        ImageUrl: finalImageUrl,
        ImageWidth: template.ImageWidth || template.Width,
        ImageHeight: template.ImageHeight || template.Height,
      }

      console.log('最终打印参数:', pageImageObject)

      // 发起图片打印
      const that = this
      bleToothManage.doPrintImage(this.data.canvasText, [pageImageObject], res => {
        console.log('图片打印回调:', res)

        // 清除打印超时定时器
        if (that.data.printTimeoutTimer) {
          clearTimeout(that.data.printTimeoutTimer)
          that.setData({
            printTimeoutTimer: null
          })
        }

        // 处理状态码
        const statusInfo = that.handlePrinterStatus(res.ResultCode)

        if (res.ResultCode === 0) {
          // 打印完成
          that.setData({
            isPrinting: false,
            printerStatus: 'connected',
            printerErrorCode: null,
            printerErrorMessage: ''
          })

          // 记录图片打印成功
          that.recordPrintResult(template, res.ResultCode, '图片打印成功')

          wx.showToast({ title: '打印成功', icon: 'success' })
        } else if (res.ResultCode === 100) {
          // 开始打印/返回尺寸信息
          if (res.ResultValue) {
            const resultValue = res.ResultValue
            if (resultValue.width && resultValue.height) {
              that.setData({
                templateWidth: resultValue.width,
                templateHeight: resultValue.height,
              })
            }
          }
          // 保持打印中状态，不做其它处理
        } else if (isSuccessStatus(res.ResultCode)) {
          // 其他成功状态码（1, 117等）
          that.setData({
            isPrinting: false,
            printerStatus: 'connected',
            printerErrorCode: null,
            printerErrorMessage: ''
          })

          // 记录图片打印成功
          that.recordPrintResult(template, res.ResultCode, '图片打印操作成功')

          wx.showToast({ title: '操作成功', icon: 'success' })
        } else if (statusInfo.isConnected) {
          // 设备仍连接，但打印失败
          that.setData({
            printerStatus: 'connected',
            printerErrorCode: res.ResultCode,
            printerErrorMessage: statusInfo.message
          })

          // 记录图片打印失败
          that.recordPrintResult(template, res.ResultCode, statusInfo.message || '图片打印失败')

          that.showUserFriendlyError(res.ResultCode, '打印失败')
        } else {
          // 设备断开或严重错误
          that.setData({
            isPrinting: false,
            printerStatus: 'error',
            printerErrorCode: res.ResultCode,
            printerErrorMessage: statusInfo.message
          })

          // 记录图片打印失败
          that.recordPrintResult(template, res.ResultCode, statusInfo.message || '设备连接断开')

          that.showUserFriendlyError(res.ResultCode, '打印失败')
        }
      }).catch(error => {
        console.log('图片打印异常:', error)

        // 清除打印超时定时器
        if (that.data.printTimeoutTimer) {
          clearTimeout(that.data.printTimeoutTimer)
        }

        // 记录图片打印异常
        that.recordPrintResult(template, 132, '图片打印异常终止')

        that.setData({
          isPrinting: false,
          printerStatus: 'error',
          printerErrorCode: 132,
          printerErrorMessage: '打印异常终止',
          printTimeoutTimer: null
        })
        wx.showToast({ title: '打印异常终止', icon: 'error' })
      })

    } catch (error) {
      console.error('图片下载失败:', error)

      // 记录图片处理失败
      this.recordPrintResult(template, 132, '图片处理失败')

      this.setData({
        isPrinting: false,
        printerStatus: 'error',
        printerErrorCode: 132,
        printerErrorMessage: '图片处理失败'
      })
      wx.showToast({ title: '图片处理失败', icon: 'error' })
    }
  },

  /**
   * 下载图片到本地临时文件
   * @param {string} imageUrl 图片地址
   * @returns {Promise} 下载结果
   */
  saveImageToLocal(imageUrl) {
    return new Promise((resolve, reject) => {
      // 方法1: 使用 wx.downloadFile (推荐)
      const fs = wx.getFileSystemManager();
      fs.saveFile({
        tempFilePath: imageUrl, // 临时路径
        filePath: `${wx.env.USER_DATA_PATH}/${imageUrl.split('/').pop()}`, // 永久路径
        success: (res) => {
          console.log('保存成功', res.savedFilePath)
          resolve({ tempFilePath: res.savedFilePath })
        },
        fail: (error) => {
          console.error('图片下载失败:', error)
          reject(error)
        }
      })

      // 方法2: 使用 fs.saveFile (备选方案)
      // const fs = wx.getFileSystemManager()
      // const fileName = `print_image_${Date.now()}.png`
      // const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`
      //
      // fs.saveFile({
      //   tempFilePath: imageUrl,
      //   filePath: filePath,
      //   success: (res) => {
      //     console.log('文件保存成功:', res.savedFilePath)
      //     resolve({ tempFilePath: res.savedFilePath })
      //   },
      //   fail: (error) => {
      //     console.error('文件保存失败:', error)
      //     reject(error)
      //   }
      // })
    })
  },

  /**
   * 检查预览图是否生成，并通过图片打印
   */
  printLabelByPerview() {
    if (!this.data.previewImagePath) {
      wx.showToast({ title: '预览图未生成', icon: 'none' })
      return
    }
    // 不再进行耗材兼容性检查，直接按图片打印
    this.executePrintByImage(this.data.previewImagePath)
  },


  /**
   * 停止打印
   */
  stopPrint() {
    if (!this.data.isPrinting) {
      wx.showToast({
        title: '当前没有打印任务',
        icon: 'none'
      })
      return
    }

    // 显示停止中提示，防止用户重复点击
    wx.showLoading({
      title: '正在停止打印...',
      mask: true
    })

    // 清除打印超时定时器
    if (this.data.printTimeoutTimer) {
      clearTimeout(this.data.printTimeoutTimer)
    }

    const that = this

    // 设置强制停止的备用定时器，确保即使停止打印API失败也能恢复UI状态
    const forceStopTimer = setTimeout(() => {
      // 停止连接监控，防止重复触发
      // that.stopConnectionMonitoring()

      console.log('停止打印超时，强制恢复状态')
      wx.hideLoading()
      that.setData({
        isPrinting: false,
        printerStatus: 'connected',
        printerErrorCode: null,
        printerErrorMessage: '',
        printTimeoutTimer: null
      })
      wx.showToast({
        title: '已强制停止打印',
        icon: 'none'
      })
    }, 5000) // 5秒后强制停止

    bleTool.stopPrint(res => {
      clearTimeout(forceStopTimer) // 清除强制停止定时器
      wx.hideLoading()
      console.log('停止打印回调:', res)
      that.setData({
        isPrinting: false,
        printerStatus: 'connected',
        printerErrorCode: null,
        printerErrorMessage: '',
        printTimeoutTimer: null
      })
      wx.showToast({
        title: '已停止打印',
        icon: 'success'
      })
    }).catch(error => {
      clearTimeout(forceStopTimer) // 清除强制停止定时器
      wx.hideLoading()
      console.error('停止打印失败:', error)
      that.setData({
        isPrinting: false,
        printTimeoutTimer: null
      })
      wx.showToast({
        title: '停止打印失败',
        icon: 'error'
      })
    })
  },
  /**
   * 断开蓝牙连接
   * @CallIn printer.wxml 中触发点击断开连接按钮点击事件
   * @CallOut bleTool.disconnectBleDevice
   */
  disconnectDevice() {
    // 防止重复操作
    if (this.data.isConnecting || this.data.isScanning) {
      wx.showToast({
        title: '操作进行中，请稍候',
        icon: 'none'
      })
      return
    }

    // 如果当前未连接，直接返回
    if (this.data.printerStatus !== 'connected') {
      wx.showToast({
        title: '设备未连接',
        icon: 'none'
      })
      return
    }

    const that = this

    // 显示确认对话框
    wx.showModal({
      title: '断开连接',
      content: '确定要断开当前打印机连接吗？',
      confirmText: '断开',
      cancelText: '取消',
      success(res) {
        if (res.confirm) {
          // 用户确认断开连接
          that.performDisconnect()
        }
      }
    })
  },

  /**
   * 执行断开连接操作
   */
/**
 * 执行断开蓝牙设备的操作。
 * 该方法会先设置设备状态为“断开中”，然后调用蓝牙工具断开连接。
 * 断开成功后，会重置所有连接相关状态，清除本地存储的设备信息，并更新模板中的设备序列号。
 * 断开失败时，会恢复设备状态为“已连接”并提示用户。
 *
 * @function performDisconnect
 * @memberof module:PrinterManager
 * @example
 * // 调用示例
 * this.performDisconnect();
 *
 * @description
 * 1. 设置状态为“断开中”。
 * 2. 调用蓝牙工具断开连接。
 * 3. 断开成功后：
 *    - 重置连接状态。
 *    - 清除本地存储的设备信息。
 *    - 更新模板中的设备序列号。
 *    - 显示断开成功的提示。
 *    - 延迟显示连接提示。
 * 4. 断开失败时：
 *    - 恢复连接状态。
 *    - 显示断开失败的提示。
 */
  performDisconnect() {
    const that = this

    // 设置断开中状态
    this.setData({
      isConnecting: true, // 复用连接状态显示"操作中"
      printerStatus: 'disconnecting'
    })

    bleTool.disconnectBleDevice().then(res => {
      console.log('断开连接结果:', res)

      // 停止在线监控
      that.stopConnectionMonitoring()

      // 重置所有连接相关状态
      that.setData({
        isConnecting: false,
        printerStatus: 'disconnected',
        printerDeviceSn: '',
        printerErrorCode: null,
        printerErrorMessage: '',
        isPrinting: false,
        materialInfo: null, // 清除耗材信息
        showDeviceSelector: false
      })

      // 清除本地存储的设备信息
      wx.removeStorageSync('lastConnectedPrinter')

      // 重置模板中的设备序列号
      const templates = that.data.templates
      templates.forEach(template => {
        template.DeviceSn = ''
      })
      that.setData({ templates })

      wx.showToast({
        title: '已断开连接',
        icon: 'success'
      })

      // 断开连接后显示连接提示
      setTimeout(() => {
        that.setData({ showConnectPrompt: true })
      }, 1500)

    }).catch(err => {
      console.log('断开连接失败:', err)

      // 补偿：判断设备是否已离线，已离线则视为断开成功并更新UI
      that.checkDeviceOnlineOnce().then(isOnline => {
        if (!isOnline) {
          // 实际已离线，按断开成功处理
          that.handleExternalDisconnect('设备已离线')
        } else {
          // 仍在线：保留原有提示
          that.setData({
            isConnecting: false,
            printerStatus: 'connected'
          })
          wx.showToast({
            title: '断开连接失败',
            icon: 'none'
          })
        }
      })
    })
  },

  /**
   * 打开官方小程序
   */
  openOfficialMiniProgram() {
    wx.navigateToMiniProgram({
      appId: 'wx0fe9df0a15f8223e', // 替换为惠而信小程序的AppID
      path: 'pages/index/index', // 替换为惠而信小程序的路径
      success: (res) => {
        console.log('打开小程序成功', res);
      },
      fail: (err) => {
        console.error('打开小程序失败', err);
      }
    });
  },

  /**
   * 打开在线商城
   */
  openOnlineStore() {
    // 检查配置是否完整
    if (onlineStoreConfig.appId === 'YOUR_TARGET_APPID' || onlineStoreConfig.appId === '') {
      wx.showModal({
        title: '配置提醒',
        content: onlineStoreConfig.errorMessages.configNotSet,
        showCancel: false
      });
      return;
    }

    // 构建传递给目标小程序的参数
    const extraData = {
      // 来源标识
      source: 'printer_miniprogram',

      // 时间戳
      timestamp: Date.now()
    };

    // 根据配置决定是否传递设备信息
    if (onlineStoreConfig.passDeviceInfo && this.data.printerDeviceSn) {
      extraData.deviceSn = this.data.printerDeviceSn;
      extraData.printerStatus = this.data.printerStatus;
    }

    // 根据配置决定是否传递标签内容
    if (onlineStoreConfig.passLabelContent) {
      extraData.labelContent = this.data.labelContent;
    }

    // 根据配置决定是否传递模板信息
    const currentTemplate = this.getCurrentTemplate()
    if (onlineStoreConfig.passTemplateInfo && currentTemplate) {
      extraData.selectedTemplate = currentTemplate;
    }

    // 添加自定义业务参数
    if (onlineStoreConfig.customParams && Object.keys(onlineStoreConfig.customParams).length > 0) {
      Object.assign(extraData, onlineStoreConfig.customParams);
    }

    // 调试模式下打印参数
    if (onlineStoreConfig.debug) {
      console.log('跳转在线商城参数:', {
        appId: onlineStoreConfig.appId,
        path: onlineStoreConfig.path,
        extraData: extraData
      });
    }

    wx.navigateToMiniProgram({
      appId: onlineStoreConfig.appId,
      path: onlineStoreConfig.path,
      extraData: extraData,
      envVersion: onlineStoreConfig.envVersion,
      success: (res) => {
        if (onlineStoreConfig.debug) {
          console.log('跳转在线商城成功', res);
        }
      },
      fail: (err) => {
        console.error('跳转在线商城失败', err);

        // 根据错误类型显示相应的提示信息
        let errorMessage = onlineStoreConfig.errorMessages.defaultError;

        if (err.errMsg && err.errMsg.includes('navigateToMiniProgram:fail can not navigateToMiniProgram in current scene')) {
          errorMessage = onlineStoreConfig.errorMessages.sceneNotSupported;
        } else if (err.errMsg && err.errMsg.includes('appId not exist')) {
          errorMessage = onlineStoreConfig.errorMessages.appNotExist;
        } else if (err.errMsg && err.errMsg.includes('path not exist')) {
          errorMessage = onlineStoreConfig.errorMessages.pathNotExist;
        }

        wx.showModal({
          title: '跳转失败',
          content: errorMessage,
          showCancel: false
        });
      }
    });
  },

  /**
   * 显示联系我们弹窗
   */
  showContact() {
    this.setData({
      showContactModal: true
    })
  },

  /**
   * 关闭联系我们弹窗
   */
  closeContact() {
    this.setData({
      showContactModal: false
    })
  },

  /**
   * 显示定制广告弹窗
   */
  showCustomAdModal() {
    this.setData({
      showCustomAdModal: true,
      adImageScrollable: false,
      adImageScrollIndicatorOpacity: 0
    })
  },

  /**
   * 广告图片加载完成
   */
  onAdImageLoad(e) {
    // 只在图片首次加载时执行一次检查
    wx.nextTick(() => {
      const query = wx.createSelectorQuery().in(this)
      query.select('#adImageContainer').boundingClientRect(containerRect => {
        query.select('#adImageContainer .ad-image').boundingClientRect(imageRect => {
          if (containerRect && imageRect) {
            const needScroll = imageRect.height > containerRect.height
            this.setData({
              adImageScrollable: needScroll,
              adImageScrollIndicatorOpacity: needScroll ? 0.7 : 0
            })
          }
        }).exec()
      }).exec()
    })
  },

  /**
   * 广告图片滚动事件
   */
  onAdImageScroll(e) {
    // 只有在可滚动时才处理滚动事件
    if (!this.data.adImageScrollable) return

    // 使用滚动事件的详细信息来判断
    const { scrollTop, scrollHeight } = e.detail
    const query = wx.createSelectorQuery().in(this)
    query.select('#adImageContainer').boundingClientRect(containerRect => {
      if (containerRect) {
        // 计算滚动进度：0-1之间的值
        const maxScrollTop = scrollHeight - containerRect.height
        const scrollProgress = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0

        // 根据滚动进度计算透明度：接近底部时透明度降低
        // 当滚动进度超过80%时开始淡出，100%时完全透明
        let opacity = 0.7
        if (scrollProgress > 0.8) {
          opacity = 0.7 * (1 - (scrollProgress - 0.8) / 0.2)
        }

        // 更新透明度
        this.setData({
          adImageScrollIndicatorOpacity: Math.max(0, opacity)
        })
      }
    }).exec()
  },

  /**
   * 关闭定制广告弹窗
   */
  closeCustomAdModal() {
    this.setData({
      showCustomAdModal: false,
      adImageScrollable: false,
      adImageScrollIndicatorOpacity: 0
    })
  },

  /**
   * 状态帮助图片加载完成
   */
  onHelpImageLoad(e) {
    // 只在图片首次加载时执行一次检查
    wx.nextTick(() => {
      const query = wx.createSelectorQuery().in(this)
      // 避免嵌套查询，改为链式查询
      query.select('#helpImageContainer').boundingClientRect()
      query.select('#helpImageContainer .ad-image').boundingClientRect()
      query.exec(res => {
        if (res && res.length >= 2) {
          const containerRect = res[0]
          const imageRect = res[1]
          if (containerRect && imageRect) {
            const needScroll = imageRect.height > containerRect.height
            // 分开设置数据，避免一次传输过多数据
            this.setData({ helpImageScrollable: needScroll })
            // 使用 nextTick 确保上一次数据设置已完成
            wx.nextTick(() => {
              this.setData({ helpImageScrollIndicatorOpacity: needScroll ? 0.7 : 0 })
            })
          }
        }
      })
    })
  },

  /**
   * 状态帮助图片滚动事件
   */
  onHelpImageScroll(e) {
    // 只有在可滚动时才处理滚动事件
    if (!this.data.helpImageScrollable) return

    // 使用滚动事件的详细信息来判断
    const { scrollTop, scrollHeight } = e.detail
    const query = wx.createSelectorQuery().in(this)
    query.select('#helpImageContainer').boundingClientRect(containerRect => {
      if (containerRect) {
        // 计算滚动进度：0-1之间的值
        const maxScrollTop = scrollHeight - containerRect.height
        const scrollProgress = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0

        // 根据滚动进度计算透明度：接近底部时透明度降低
        // 当滚动进度超过80%时开始淡出，100%时完全透明
        let opacity = 0.7
        if (scrollProgress > 0.8) {
          opacity = 0.7 * (1 - (scrollProgress - 0.8) / 0.2)
        }

        // 更新透明度
        this.setData({
          helpImageScrollIndicatorOpacity: Math.max(0, opacity)
        })
      }
    }).exec()
  },

  /**
   * 联系客服进行定制
   */
  contactForCustom() {
    this.setData({
      showCustomAdModal: false
    })
    // 延迟显示联系我们弹窗
    setTimeout(() => {
      this.showContact()
    }, 300)
  },

  /**
   * 复制联系信息
   */
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 保存预览图到本地
   */
  savePreviewImage() {
    if (!this.data.previewImagePath) {
      wx.showToast({
        title: '预览图未生成',
        icon: 'none'
      })
      return
    }

    wx.downloadFile({
      url: this.data.previewImagePath,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.showToast({
                title: '图片保存成功',
                icon: 'success'
              })
            },
            fail: (err) => {
              console.error('保存图片失败:', err)
              wx.showToast({
                title: '保存失败，请重试',
                icon: 'none'
              })
            }
          })
        }
      },
      fail: (err) => {
        console.error('下载图片失败:', err)
        wx.showToast({
          title: '下载图片失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 刷新耗材信息
   * 手动刷新当前耗材信息，用于用户更换耗材后的情况
   */
  refreshMaterialInfo() {
    if (this.data.printerStatus !== 'connected') {
      wx.showToast({
        title: '请先连接打印机',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '获取耗材信息...'
    })

    this.getMaterialInfo().then(() => {
      wx.hideLoading()
      this.performCompatibilityCheck()
      wx.showToast({
        title: '耗材信息已更新',
        icon: 'success'
      })
    }).catch(error => {
      wx.hideLoading()
      console.error('刷新耗材信息失败:', error)
      wx.showToast({
        title: '获取耗材信息失败',
        icon: 'none'
      })
    })
  },

  /**
   * 调试方法：手动检查兼容性
   * 可以在控制台调用 getCurrentPages()[0].debugCheckCompatibility()
   */
  debugCheckCompatibility() {
    console.log('=== 手动兼容性检查 ===')
    console.log('当前状态:', {
      materialMismatch: this.data.materialMismatch,
      forcePrint: this.data.forcePrint,
      printerStatus: this.data.printerStatus,
      selectedTemplateId: this.data.selectedTemplateId
    })

    if (this.data.printerStatus === 'connected') {
      this.checkMaterialCompatibility(true) // 强制刷新
    } else {
      console.log('打印机未连接，无法检查兼容性')
    }
  },

  /**
   * 测试设备检查API（开发调试用）
   * 可以在控制台调用 getCurrentPages()[0].testDeviceCheck()
   */
  testDeviceCheck() {
    // 模拟一个测试设备
    const testDevice = {
      name: 'HP001234567890', // 测试序列号
      deviceId: 'test-device-id'
    }

    console.log('开始测试设备检查API...')

    checkPrinterDevice(testDevice).then(result => {
      console.log('设备检查测试结果:', result)

      let message = ''
      let title = '设备检查测试结果'

      if (result.success && result.isInStock) {
        title = '✅ 设备检查成功'
        message = `设备在库，可以连接\n\n设备信息：\n型号：${result.deviceInfo?.model || 'N/A'}\n序列号：${result.deviceInfo?.sn || 'N/A'}\n查询次数：${result.deviceInfo?.queryCount || 'N/A'}\n备注：${result.deviceInfo?.remark || 'N/A'}`
      } else if (result.success && !result.isInStock) {
        title = '❌ 设备不在库'
        message = `设备检查成功，但设备不在官方库中\n无法连接此设备`
      } else {
        title = '❌ 设备检查失败'
        message = `错误码：${result.code || 'N/A'}\n错误信息：${result.message || '未知错误'}\n原始消息：${result.originalMessage || 'N/A'}`
      }

      wx.showModal({
        title: title,
        content: message,
        showCancel: false
      })
    }).catch(error => {
      console.error('设备检查测试失败:', error)
      wx.showModal({
        title: '❌ 网络请求失败',
        content: `请检查网络连接\n错误详情：${error.message || error}`,
        showCancel: false
      })
    })
  },

  /**
   * 设置是否检查打印机绑定（开发调试用）
   * 可以在控制台调用 getCurrentPages()[0].setCheckPrinterBinding(true/false)
   */
  setCheckPrinterBinding(enabled) {
    const checkPrinterBinding = !!enabled

    this.setData({
      checkPrinterBinding: checkPrinterBinding
    })

    // 保存到本地存储
    wx.setStorageSync('checkPrinterBinding', checkPrinterBinding)

    const status = enabled ? '启用' : '禁用'
    console.log(`设备检查功能已${status}`)

    wx.showToast({
      title: `设备检查功能已${status}`,
      icon: 'success',
      duration: 2000
    })
  },

  /**
   * 获取当前设备检查状态（开发调试用）
   * 可以在控制台调用 getCurrentPages()[0].getCheckPrinterBindingStatus()
   */
  getCheckPrinterBindingStatus() {
    const status = this.data.checkPrinterBinding ? '启用' : '禁用'
    console.log(`当前设备检查功能状态: ${status}`)

    wx.showModal({
      title: '设备检查功能状态',
      content: `当前状态: ${status}\n\n${this.data.checkPrinterBinding ? '连接前会检查设备是否在官方库中' : '连接前不会检查设备，直接连接'}`,
      showCancel: false
    })

    return this.data.checkPrinterBinding
  },

  /**
   * 记录打印结果
   * @param {Object} printTemplate - 打印模板对象
   * @param {number} resultCode - 打印结果代码
   * @param {string} resultDescription - 打印结果描述
   */
  async recordPrintResult(printTemplate, resultCode, resultDescription) {
    try {
      // 获取用户OpenId
      const openId = this.userOpenId || ''

      // 获取打印机SN
      const printerSN = this.data.printerDeviceSn || ''

      // 获取模板ID（优先使用TemplateId，如果没有则使用模板名称的hash值）
      let templateId = printTemplate.TemplateId
      if (!templateId && printTemplate.TemplateName) {
        // 简单的字符串hash算法生成ID
        templateId = this.hashString(printTemplate.TemplateName)
      }

      // 获取打印份数
      const printCount = printTemplate.Copies || 1

      // 构建打印记录数据
      const printData = {
        openId: openId,
        printerSN: printerSN,
        templateId: templateId || 0,
        printCount: printCount,
        resultCode: String(resultCode),
        resultDescription: resultDescription
      }

      console.log('记录打印结果:', printData)

      // 调用API记录打印结果
      const result = await addPrintRecord(printData)

      if (result.success) {
        console.log('打印记录保存成功:', result)
      } else {
        console.warn('打印记录保存失败:', result.message)
      }
    } catch (error) {
      console.error('记录打印结果异常:', error)
    }
  },

  /**
   * 简单的字符串hash算法
   * @param {string} str - 要hash的字符串
   * @returns {number} hash值
   */
  hashString(str) {
    let hash = 0
    if (str.length === 0) return hash
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  },

  /**
   * 测试不同错误场景（开发调试用）
   * 可以在控制台调用 getCurrentPages()[0].testErrorScenarios()
   */
  testErrorScenarios() {
    const scenarios = [
      { name: '空设备名称', device: { name: '', deviceId: '' } },
      { name: '不存在的设备', device: { name: 'INVALID_DEVICE_SN', deviceId: 'invalid' } },
      { name: '正常设备', device: { name: 'HP001234567890', deviceId: 'test' } }
    ]

    let currentIndex = 0

    const testNext = () => {
      if (currentIndex >= scenarios.length) {
        wx.showModal({
          title: '测试完成',
          content: '所有错误场景测试完成，请查看控制台日志',
          showCancel: false
        })
        return
      }


      const scenario = scenarios[currentIndex]
      console.log(`\n=== 测试场景 ${currentIndex + 1}: ${scenario.name} ===`)

      checkPrinterDevice(scenario.device).then(result => {
        console.log(`场景 "${scenario.name}" 结果:`, result)
        currentIndex++
        setTimeout(testNext, 1000) // 延迟1秒测试下一个场景
      }).catch(error => {
        console.error(`场景 "${scenario.name}" 异常:`, error)
        currentIndex++
        setTimeout(testNext, 1000)
      })
    }

    wx.showModal({
      title: '开始错误场景测试',
      content: `将测试 ${scenarios.length} 个场景，请查看控制台输出`,
      showCancel: false,
      success: () => {
        testNext()
      }
    })
  },

  // 显示模板帮助提示
  showTemplateHelpTip() {
    this.setData({
      showTemplateHelp: true
    })

    // 3秒后自动隐藏提示
    setTimeout(() => {
      this.setData({
        showTemplateHelp: false
      })
    }, 3000)
  }
})
