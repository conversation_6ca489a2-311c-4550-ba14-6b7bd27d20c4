/**
 * 打印机异常状态操作说明（图片）配置
 * - 支持按错误码(code)或特殊状态(state)匹配
 * - 返回图片等信息，用于在异常状态栏的“?”弹窗展示
 */

const helpTips = {
  // 按错误码配置
  byCode: {
    // 示例：蓝牙不可用
    105: {
      title: '开启蓝牙后重试',
      image: 'https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/help_tips.jpg'
    },
    // 示例：请关闭耗材仓盖
    126: {
      title: '请关闭耗材仓盖',
      image: 'https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/help_tips.jpg'
    }
  },

  // 按特殊状态配置
  byState: {
    // 未找到打印机（扫描为空）
    NO_PRINTER_FOUND: {
      title: '未找到打印机的解决方法',
      image: 'https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/help_tips.jpg'
    },
    // 初始未连接状态
    DISCONNECTED: {
      title: '如何连接打印机',
      image: 'https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/help_tips.jpg'
    },
    // 已连接但存在打印警告/耗材问题
    CONNECTED_WITH_WARNING: {
      title: '已连接但无法打印的排查',
      image: 'https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/help_tips.jpg'
    },
    // 兜底的一般错误
    GENERIC_ERROR: {
      title: '打印异常排查',
      image: 'https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/help_tips.jpg'
    }
  }
}

/**
 * 根据当前上下文获取操作说明
 * @param {{code?: number|null, status?: string, message?: string}} ctx
 * @returns {{title?: string, image: string}|null}
 */
function getHelpTipByContext(ctx = {}) {
  const code = typeof ctx.code === 'number' ? ctx.code : null
  const status = ctx.status || ''
  const message = ctx.message || ''

  // 1) 优先按错误码
  if (code != null && helpTips.byCode[code]) {
    return helpTips.byCode[code]
  }

  // 2) 特殊文案判断
  if (!code && message && message.indexOf('未找到可用的打印机') >= 0) {
    return helpTips.byState.NO_PRINTER_FOUND || null
  }

  // 3) 按状态匹配
  if (status === 'disconnected') return helpTips.byState.DISCONNECTED || null
  if (status === 'error') return helpTips.byState.GENERIC_ERROR || null
  if (status === 'connected' && message) return helpTips.byState.CONNECTED_WITH_WARNING || null

  return null
}

function hasHelpTipForContext(ctx = {}) {
  return !!getHelpTipByContext(ctx)
}

module.exports = {
  helpTips,
  getHelpTipByContext,
  hasHelpTipForContext
}

